{"name": "hi<PERSON><PERSON>", "description": "hioshop - open source mini program shop", "version": "1.0.0", "scripts": {"start": "node development.js", "start:prod-debug": "node prod-debug.js", "compile": "babel --no-babelrc src/ --presets think-node --out-dir app/", "lint": "eslint src/", "lint-fix": "eslint --fix src/"}, "dependencies": {"alipay-sdk": "3.6.2", "axios": "0.18.1", "gm": "^1.23.0", "ioredis": "^5.6.0", "jsonwebtoken": "^8.0.0", "kcors": "^2.2.1", "lodash": "^4.17.4", "md5": "^2.2.1", "mime-types": "^2.1.24", "moment": "^2.18.1", "nanoid": "^2.1.1", "node-wget": "^0.4.3", "pinyin": "^2.9.0", "querystring": "^0.2.0", "request": "^2.81.0", "request-promise": "^4.2.1", "tencentcloud-sdk-nodejs-sms": "^4.1.24", "think-cache": "1.1.0", "think-cache-file": "1.1.0", "think-cache-redis": "1.2.6", "think-logger3": "1.1.1", "think-model": "1.2.2", "think-model-mysql": "1.0.6", "think-view": "^1.0.11", "think-view-nunjucks": "^1.0.7", "thinkjs": "3.2.7", "wechatpay-node-v3": "^2.2.1", "weixinpay": "^1.0.12", "xml2js": "^0.4.19"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-preset-think-node": "^1.0.0", "eslint": "^4.2.0", "eslint-config-think": "^1.0.0", "node-notifier": "^5.0.2", "think-babel": "^1.0.3", "think-inspect": "0.0.2", "think-watcher": "^3.0.0"}, "repository": "", "license": "MIT", "engines": {"node": ">=6.0.0"}, "readmeFilename": "README.md"}