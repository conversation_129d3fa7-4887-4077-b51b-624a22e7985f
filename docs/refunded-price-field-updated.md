# 订单表新增 refunded_price 字段（修正版）

## 字段说明

在 `order` 表中新增 `refunded_price` 字段，用于记录订单的已退款总金额。

### 字段定义

```sql
ALTER TABLE `order` ADD COLUMN `refunded_price` DECIMAL(10,2) DEFAULT 0.00 COMMENT '已退款总金额';
```

## 业务逻辑

### 1. 退款前检查 (所有退款方法)

**退款金额验证**：
```javascript
const currentRefundedPrice = parseFloat(orderInfo.refunded_price || 0);
const refundAmountFloat = parseFloat(refundAmount);
const actualPrice = parseFloat(orderInfo.actual_price);

// 检查退款金额是否超出限制
if (currentRefundedPrice + refundAmountFloat > actualPrice) {
    return this.fail(`退款金额超出限制，已退款：${currentRefundedPrice}元，本次退款：${refundAmountFloat}元，订单总额：${actualPrice}元`);
}
```

### 2. 不同支付方式的退款处理

#### 余额支付 (`pay_type = 'balance'`)
**立即更新**：余额支付退款是同步的，立即更新 `refunded_price`
```javascript
const newRefundedPrice = currentRefundedPrice + refundAmount;
await this.model('order').update({
    refunded_price: newRefundedPrice,
    order_status: isFullRefund ? 203 : 206
});
```

#### 支付宝支付 (`pay_type = 'ali'`)
**发起退款**：只发起退款请求，不更新 `refunded_price`
```javascript
// 发起支付宝退款，等待回调确认
result = await alipaySdk.exec('alipay.trade.refund', {...});
// 不立即更新 refunded_price
```

**回调确认**：在 `aliPayRefund` 回调方法中更新
```javascript
const newRefundedPrice = currentRefundedPrice + refundAmount;
await this.model('order').update({
    refunded_price: newRefundedPrice,
    order_status: isFullRefund ? 203 : 206
});
```

#### 微信支付 (`pay_type = 'wx'`)
**发起退款**：只发起退款请求，不更新 `refunded_price`
```javascript
// 发起微信退款，等待回调确认
result = await wechatpay.refunds({...});
// 不立即更新 refunded_price
```

**回调确认**：在 `afterWxPayRefund` 回调方法中更新
```javascript
const refundAmount = parseFloat(orderInfo.refund_fee) / 100; // 微信金额单位转换
const newRefundedPrice = currentRefundedPrice + refundAmount;
await this.model('order').update({
    refunded_price: newRefundedPrice,
    order_status: isFullRefund ? 203 : 206
});
```

## 退款流程

### 部分退款流程
1. **管理员发起退款** → 检查退款金额合法性
2. **余额支付**：立即更新 `refunded_price` 和订单状态
3. **支付宝/微信**：发起退款请求，等待回调
4. **收到回调**：更新 `refunded_price` 和订单状态

### 全额退款流程
1. **管理员发起退款** → 计算退款金额
2. **余额支付**：立即更新 `refunded_price` 和订单状态
3. **支付宝/微信**：发起退款请求，等待回调
4. **收到回调**：更新 `refunded_price` 和订单状态

## 关键修改点

### 发起退款时
- **余额支付**：立即更新 `refunded_price`
- **支付宝/微信**：不更新 `refunded_price`，等待回调

### 回调处理时
- **支付宝回调** (`aliPayRefund`)：更新 `refunded_price`
- **微信回调** (`afterWxPayRefund`)：更新 `refunded_price`

## 数据一致性保证

1. **余额支付**：使用数据库事务确保余额和订单状态同步更新
2. **第三方支付**：通过回调机制确保退款成功后才更新记录
3. **重复回调**：回调方法具有幂等性，重复调用不会重复累加退款金额

## 优势

1. **准确记录**：只有在退款真正成功时才记录退款金额
2. **防止超额退款**：通过累计金额检查防止退款超过订单总额
3. **异步安全**：第三方支付通过回调确认，确保数据准确性
4. **便于对账**：准确的退款记录便于财务对账和统计分析