# 交易日志表增强说明

## 概述

为了配合支付流水号功能，需要在 `petshop_transaction_log` 表中添加 `pay_trade_no` 字段，以便更好地追踪和关联支付交易记录。

## 数据库变更

### 添加字段

```sql
ALTER TABLE `petshop_transaction_log` 
ADD COLUMN `pay_trade_no` VARCHAR(64) NULL DEFAULT NULL COMMENT '支付流水号' AFTER `order_sn`,
ADD INDEX `idx_transaction_pay_trade_no` (`pay_trade_no`);
```

## 字段说明

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| pay_trade_no | VARCHAR | 64 | NULL | 支付流水号，与订单表的pay_trade_no对应 |

## 使用场景

### 1. 支付成功记录

```javascript
// 微信支付成功
await this.model('transaction_log').add({
    order_id: order.id,
    order_sn: order.order_sn,
    pay_trade_no: payTradeNo, // 微信支付流水号
    user_id: order.user_id,
    log_type: 'CS', // 消费
    pay_status: 201, // 已付款
    pay_type: 'wx',
    amount: orderInfo.amount.total / 100,
    pay_id: orderInfo.transaction_id, // 微信平台交易号
    // ...
});

// 支付宝支付成功
await this.model('transaction_log').add({
    order_id: order.id,
    order_sn: order.order_sn,
    pay_trade_no: payTradeNo, // 支付宝支付流水号
    user_id: order.user_id,
    log_type: 'CS',
    pay_status: 201,
    pay_type: 'ali',
    amount: orderInfo.total_amount,
    pay_id: orderInfo.trade_no, // 支付宝平台交易号
    // ...
});

// 余额支付成功
await this.model('transaction_log').add({
    order_id: order.id,
    order_sn: order.order_sn,
    pay_trade_no: pay_trade_no, // 余额支付流水号
    user_id: userId,
    log_type: 'CS',
    pay_status: 201,
    pay_type: 'balance',
    amount: orderAmount,
    pay_id: pay_trade_no, // 余额支付使用流水号作为支付ID
    // ...
});
```

### 2. 退款记录

```javascript
// 退款记录关联原支付流水号
await this.model('transaction_log').add({
    order_id: order.id,
    order_sn: order.order_sn,
    pay_trade_no: order.pay_trade_no, // 关联原支付流水号
    user_id: order.user_id,
    log_type: 'RF', // 退款
    pay_status: 203, // 已退款
    pay_type: 'wx',
    amount: refundAmount,
    pay_id: orderInfo.transaction_id,
    refund_id: orderInfo.refund_id,
    // ...
});
```

## 数据关系

### 支付流水号的层级关系

```
订单表 (petshop_order)
├── order_sn: ORDER20250726001
├── pay_trade_no: ORDER20250726001-1706234567890-abc123
└── 关联交易记录

交易日志表 (petshop_transaction_log)
├── 支付记录
│   ├── pay_trade_no: ORDER20250726001-1706234567890-abc123
│   ├── pay_id: wx_transaction_id_from_wechat
│   └── log_type: CS
└── 退款记录
    ├── pay_trade_no: ORDER20250726001-1706234567890-abc123 (相同)
    ├── refund_id: wx_refund_id_from_wechat
    └── log_type: RF
```

### 字段对应关系

| 场景 | pay_trade_no | pay_id | refund_id |
|------|-------------|--------|-----------|
| 微信支付 | 我们的支付流水号 | 微信交易号 | - |
| 支付宝支付 | 我们的支付流水号 | 支付宝交易号 | - |
| 余额支付 | 我们的支付流水号 | 我们的支付流水号 | - |
| 微信退款 | 原支付流水号 | 微信交易号 | 微信退款号 |
| 支付宝退款 | 原支付流水号 | 支付宝交易号 | 支付宝退款号 |
| 余额退款 | 原支付流水号 | 原支付流水号 | - |

## 查询优化

### 1. 按支付流水号查询交易记录

```javascript
// 查询某个支付流水号的所有交易记录（支付+退款）
const transactions = await this.model('transaction_log').where({
    pay_trade_no: 'ORDER20250726001-1706234567890-abc123'
}).order('create_time ASC').select();
```

### 2. 按订单查询交易记录

```javascript
// 查询某个订单的所有交易记录
const transactions = await this.model('transaction_log').where({
    order_id: 12345
}).order('create_time ASC').select();
```

### 3. 统计分析

```javascript
// 按支付方式统计交易金额
const stats = await this.model('transaction_log')
    .field('pay_type, SUM(amount) as total_amount, COUNT(*) as count')
    .where({
        log_type: 'CS',
        pay_status: 201
    })
    .group('pay_type')
    .select();
```

## 数据迁移

### 为现有交易记录补充支付流水号

```sql
-- 为现有交易记录设置支付流水号（从订单表获取）
UPDATE `petshop_transaction_log` tl
JOIN `petshop_order` o ON tl.order_id = o.id
SET tl.`pay_trade_no` = o.`pay_trade_no`
WHERE tl.`pay_trade_no` IS NULL 
  AND o.`pay_trade_no` IS NOT NULL;

-- 为没有支付流水号的订单和交易记录设置默认值（使用订单号）
UPDATE `petshop_order` 
SET `pay_trade_no` = `order_sn` 
WHERE `pay_trade_no` IS NULL AND `order_status` >= 201;

UPDATE `petshop_transaction_log` tl
JOIN `petshop_order` o ON tl.order_id = o.id
SET tl.`pay_trade_no` = o.`order_sn`
WHERE tl.`pay_trade_no` IS NULL;
```

## 监控和分析

### 1. 支付成功率监控

```sql
-- 按支付方式统计成功率
SELECT 
    pay_type,
    COUNT(*) as total_attempts,
    SUM(CASE WHEN pay_status = 201 THEN 1 ELSE 0 END) as success_count,
    ROUND(SUM(CASE WHEN pay_status = 201 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM petshop_transaction_log 
WHERE log_type = 'CS' 
  AND DATE(create_time) = CURDATE()
GROUP BY pay_type;
```

### 2. 退款率分析

```sql
-- 计算退款率
SELECT 
    pay_type,
    COUNT(DISTINCT CASE WHEN log_type = 'CS' THEN pay_trade_no END) as payment_count,
    COUNT(DISTINCT CASE WHEN log_type = 'RF' THEN pay_trade_no END) as refund_count,
    ROUND(COUNT(DISTINCT CASE WHEN log_type = 'RF' THEN pay_trade_no END) * 100.0 / 
          COUNT(DISTINCT CASE WHEN log_type = 'CS' THEN pay_trade_no END), 2) as refund_rate
FROM petshop_transaction_log 
WHERE DATE(create_time) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY pay_type;
```

### 3. 支付流水号唯一性检查

```sql
-- 检查支付流水号是否有重复
SELECT pay_trade_no, COUNT(*) as count
FROM petshop_transaction_log 
WHERE pay_trade_no IS NOT NULL
GROUP BY pay_trade_no
HAVING COUNT(*) > 2; -- 正常情况下，一个支付流水号最多对应2条记录（支付+退款）
```

## 注意事项

1. **数据一致性**：确保订单表和交易日志表的 `pay_trade_no` 字段保持一致
2. **索引性能**：添加的索引会提高查询性能，但会略微影响写入性能
3. **历史数据**：现有数据的 `pay_trade_no` 为 NULL，需要通过数据迁移脚本处理
4. **监控告警**：建议对支付流水号重复、交易记录异常等情况设置监控告警
5. **备份策