/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-26 18:45:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-26 21:08:41
 * @FilePath: /petshop-server/src/common/model/admin.js
 * @Description: 管理员模型
 */

const Base = require('./base.js');

module.exports = class extends Base {
    /**
     * 获取所有有效的管理员手机号
     * @returns {Promise<Array>} 管理员手机号列表
     */
    async getActiveAdminMobiles() {
        try {
            const adminList = await this.where({
                is_delete: 0,
            }).field('mobile').select();

            return adminList
                .filter(admin => admin.mobile && admin.mobile.trim())
                .map(admin => admin.mobile.trim());
        } catch (error) {
            think.logger.error('获取管理员手机号失败:', error);
            return [];
        }
    }

    /**
     * 根据手机号获取管理员信息
     * @param {string} mobile 手机号
     * @returns {Promise<Object|null>} 管理员信息
     */
    async getAdminByMobile(mobile) {
        try {
            if (!mobile || !mobile.trim()) {
                return null;
            }

            return await this.where({
                mobile: mobile.trim(),
                is_delete: 0,
            }).find();
        } catch (error) {
            think.logger.error('根据手机号获取管理员信息失败:', error);
            return null;
        }
    }
};