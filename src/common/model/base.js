/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-15 00:42:58
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-21 00:02:42
 * @FilePath: /petshop-server/src/common/service/service.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const { throwError } = require('../config/utils');
module.exports = class extends think.Model {
    /**
     * 初始化方法
     * @param {Object} config 配置
     */
    init(config) {
      super.init(config);
    }

    /**
     * 分页查询
     * @param {Object} where 查询条件
     * @param {Number} page 当前页码
     * @param {Number} pageSize 每页条数
     * @param {Object} options 选项
     * @return {Promise<Object>} {list, count, page, pageSize, totalPage}
     */
    async page(where = {}, page = 1, pageSize = 10, options = {}) {
      const count = await this.where(where).count();
      const list = await this.where(where)
        .page(page, pageSize)
        .order(options.order || 'id DESC')
        .select();

      return {
        list,
        count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPage: Math.ceil(count / pageSize)
      };
    }

    /**
     * 根据ID获取详情
     * @param {Number} id ID
     * @return {Promise<Object>} 数据详情
     */
    async getById(id) {
      return this.where({ id }).find();
    }

    /**
     * 根据ID更新
     * @param {Number} id ID
     * @param {Object} data 更新数据
     * @return {Promise<Number>} 影响行数
     */
    async updateById(id, data) {
      return this.where({ id }).update(data);
    }

    /**
     * 根据ID删除
     * @param {Number} id ID
     * @return {Promise<Number>} 影响行数
     */
    async deleteById(id) {
      return this.where({ id }).update({ is_delete: 1 });
    }
};