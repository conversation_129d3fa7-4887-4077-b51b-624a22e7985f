/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-21 00:49:46
 * @FilePath: /petshop-server/src/api/controller/base.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const { throwError } = require('../config/utils');
module.exports = class extends think.Controller {
  constructor(ctx) {
    super(ctx);
    this.throwError = throwError; // 挂载 throwError 到 this 上
  }
	async __before() {
	}
	/**
	 * 获取时间戳
	 * @returns {Number}
	 */
	getTime() {
		return parseInt(Date.now() / 1000);
	}
  
	/**
	 * 获取当前登录用户的id
	 * @returns {*}
	 */
	getLoginUserId() {
    const {user} = this.ctx.state
    if (!user) {
      return '';
    }
    return user.id || '';
	}

  /**
	 * 获取当前登录用户是否代理商，只有app用户才存在此逻辑
	 * @returns {*}
	 */
	getUserAgent() {
    const {user} = this.ctx.state
    if (!user) {
      return 0;
    }
    return user.is_agent || 0;
	}

  /**
   * 获取 model 实例，并自动传递事务 session
   * @param {string} modelName - model 名称
   * @returns {Object} model 实例
   */
  getModel(modelName) {
    try {
      const session = this.ctx.__transaction_session;
      const modelInstance = this.model(modelName);
      if (session) {
        return modelInstance.db(session);
      }
      throw new Error('事务 session 为空');
    } catch (error) {
      throw error;
    }
  }

  /**
   * 事务封装，支持多个 model 共享同一事务
   * @param {session} callback 事务执行回调函数
   */
  async transaction(modelName, callback) {
    const startTime = Date.now();
    let session = this.ctx.__transaction_session;
    let timeoutId = null;

    // 如果没有事务，则创建一个新的事务
    if (!session) {
      session = await this.model(modelName).db();
      await session.startTrans();
      this.ctx.__transaction_session = session;
      
      // 设置事务超时
      timeoutId = setTimeout(() => {
        // 只有在 session 还存在且未被清理时才执行回滚
        if (this.ctx.__transaction_session === session) {
          const duration = Date.now() - startTime;
          think.logger.error(`事务超时自动回滚，执行时间: ${duration}ms`);
          session.rollback().catch(e => think.logger.error('回滚失败', e));
          this.ctx.__transaction_session = null;
          this.ctx.__transaction_timeout = null;
        }
      }, 30000); // 30秒超时（从60秒调整为30秒）
      
      this.ctx.__transaction_timeout = timeoutId;
    }
    
    try {
      // 执行业务逻辑
      const result = await callback(session);
      // 提交事务
      await session.commit();
      const duration = Date.now() - startTime;
      think.logger.info(`事务执行成功，耗时: ${duration}ms`);
      return result;
    } catch (error) {
      // 出现异常，回滚事务
      await session.rollback();
      const duration = Date.now() - startTime;
      think.logger.error(`事务执行失败，耗时: ${duration}ms`, error);
      throw error;
    } finally {
      // 清理事务 session 和超时
      if (this.ctx.__transaction_timeout) {
        clearTimeout(this.ctx.__transaction_timeout);
        this.ctx.__transaction_timeout = null;
      }
      this.ctx.__transaction_session = null;
    }
  }
};
