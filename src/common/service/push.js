/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-26 18:30:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-26 23:57:53
 * @FilePath: /petshop-server/src/common/service/push.js
 * @Description: 推送通知服务
 */

const Base = require('./base.js');
const { JPushAsync, JPush } = require('jpush-async');

module.exports = class extends Base {
    constructor() {
        super();
        // JPush 配置
        this.jpushClient = JPushAsync.buildClient({
            appKey: think.config('jpush.appKey') || 'your_jpush_app_key',
            masterSecret: think.config('jpush.masterSecret') || 'your_jpush_master_secret',
            retryTimes: 3,
            isDebug: think.env !== 'production'
        });
    }

    /**
     * 发送订单支付成功通知给管理员
     * @param {Object} orderInfo 订单信息
     * @param {string} payType 支付类型 (wx/ali/balance)
     * @returns {Promise<boolean>} 发送结果
     */
    async sendOrderPaymentNotification(orderInfo, payType = '') {
        try {
            // 获取所有管理员的手机号
            const adminModel = this.model('admin');
            const aliasList = await adminModel.getActiveAdminMobiles();

            if (aliasList.length === 0) {
                think.logger.warn('没有找到有效的管理员手机号');
                return false;
            }

            // 构建推送消息
            const payTypeText = this.getPayTypeText(payType);
            const message = {
                title: '新订单支付成功',
                content: `订单号：${orderInfo.order_sn}\n支付方式：${payTypeText}\n支付金额：¥${orderInfo.actual_price}\n请及时处理订单`,
                extras: {
                    type: 'order_payment',
                    order_id: orderInfo.id,
                    order_sn: orderInfo.order_sn,
                    pay_type: payType,
                    amount: orderInfo.actual_price
                }
            };

            // 发送推送
            const result = await this.sendPushNotification(aliasList, message);

            if (result) {
                think.logger.info(`订单支付通知发送成功，订单号：${orderInfo.order_sn}，通知管理员数量：${aliasList.length}`);
            } else {
                think.logger.error(`订单支付通知发送失败，订单号：${orderInfo.order_sn}`);
            }

            return result;
        } catch (error) {
            think.logger.error('发送订单支付通知时出错:', error);
            return false;
        }
    }

    /**
     * 通用推送方法
     * @param {Array} aliasList 推送别名列表
     * @param {Object} message 消息内容
     * @returns {Promise<boolean>} 发送结果
     */
    async sendPushNotification(aliasList, message) {
        try {
            if (!Array.isArray(aliasList) || aliasList.length === 0) {
                think.logger.warn('推送别名列表为空');
                return false;
            }

            // 构建推送请求
            const pushPayload = this.jpushClient.push()
                .setPlatform(JPushAsync.ALL)
                .setAudience(JPushAsync.alias(aliasList))
                .setNotification(
                    JPushAsync.ios({
                        title: message.title,
                        body: message.content,
                    }, 'sound', 1, true, message.extras),
                    JPushAsync.android(message.content, message.title, 1, message.extras)
                )
                .setOptions(null, 86400, null, think.env === 'production');

            // 发送推送
            const result = await this.jpushClient.sendPush(pushPayload);

            if (result && result.msg_id) {
                think.logger.info('JPush推送发送成功:', {
                    msg_id: result.msg_id,
                    sendno: result.sendno,
                    aliases: aliasList
                });
                return true;
            } else {
                think.logger.error('JPush推送发送失败:', result);
                return false;
            }
        } catch (error) {
            think.logger.error('JPush推送发送异常:', error);
            return false;
        }
    }

    /**
     * 发送订单退款申请通知给管理员
     * @param {Object} orderInfo 订单信息
     * @returns {Promise<boolean>} 发送结果
     */
    async sendOrderRefundApplyNotification(orderInfo) {
        try {
            // 获取所有管理员的手机号
            const adminModel = this.model('admin');
            const aliasList = await adminModel.getActiveAdminMobiles();

            if (aliasList.length === 0) {
                think.logger.warn('没有找到有效的管理员手机号');
                return false;
            }

            // 构建推送消息
            const payTypeText = this.getPayTypeText(orderInfo.pay_type);
            const message = {
                title: '用户申请订单退款',
                content: `订单号：${orderInfo.order_sn}\n支付方式：${payTypeText}\n订单金额：¥${orderInfo.actual_price}\n用户已申请退款，请及时处理`,
                extras: {
                    type: 'order_refund_apply',
                    order_id: orderInfo.id,
                    order_sn: orderInfo.order_sn,
                    pay_type: orderInfo.pay_type,
                    amount: orderInfo.actual_price,
                    user_id: orderInfo.user_id
                }
            };

            // 发送推送
            const result = await this.sendPushNotification(aliasList, message);

            if (result) {
                think.logger.info(`订单退款申请通知发送成功，订单号：${orderInfo.order_sn}，通知管理员数量：${aliasList.length}`);
            } else {
                think.logger.error(`订单退款申请通知发送失败，订单号：${orderInfo.order_sn}`);
            }

            return result;
        } catch (error) {
            think.logger.error('发送订单退款申请通知时出错:', error);
            return false;
        }
    }

    /**
     * 发送退货运单号提交通知给管理员
     * @param {Object} orderInfo 订单信息
     * @param {Object} expressInfo 物流信息
     * @returns {Promise<boolean>} 发送结果
     */
    async sendRefundExpressNotification(orderInfo, expressInfo) {
        try {
            // 获取所有管理员的手机号
            const adminModel = this.model('admin');
            const aliasList = await adminModel.getActiveAdminMobiles();

            if (aliasList.length === 0) {
                think.logger.warn('没有找到有效的管理员手机号');
                return false;
            }

            // 构建推送消息
            const message = {
                title: '用户提交退货运单号',
                content: `订单号：${orderInfo.order_sn}\n快递公司：${expressInfo.shipper_name}\n运单号：${expressInfo.logistic_code}\n寄件人：${expressInfo.sender_name}\n联系电话：${expressInfo.sender_phone}\n请及时查收退货商品`,
                extras: {
                    type: 'refund_express_submit',
                    order_id: orderInfo.id,
                    order_sn: orderInfo.order_sn,
                    logistic_code: expressInfo.logistic_code,
                    shipper_name: expressInfo.shipper_name,
                    shipper_code: expressInfo.shipper_code,
                    sender_name: expressInfo.sender_name,
                    sender_phone: expressInfo.sender_phone
                }
            };

            // 发送推送
            const result = await this.sendPushNotification(aliasList, message);

            if (result) {
                think.logger.info(`退货运单号提交通知发送成功，订单号：${orderInfo.order_sn}，运单号：${expressInfo.logistic_code}，通知管理员数量：${aliasList.length}`);
            } else {
                think.logger.error(`退货运单号提交通知发送失败，订单号：${orderInfo.order_sn}`);
            }

            return result;
        } catch (error) {
            think.logger.error('发送退货运单号提交通知时出错:', error);
            return false;
        }
    }

    /**
     * 发送代理商申请通知给管理员
     * @param {Object} agentApplyInfo 代理商申请信息
     * @param {Object} userInfo 用户信息
     * @returns {Promise<boolean>} 发送结果
     */
    async sendAgentApplyNotification(agentApplyInfo, userInfo) {
        try {
            // 获取所有管理员的手机号
            const adminModel = this.model('admin');
            const aliasList = await adminModel.getActiveAdminMobiles();

            if (aliasList.length === 0) {
                think.logger.warn('没有找到有效的管理员手机号');
                return false;
            }

            // 构建推送消息
            const message = {
                title: '新的代理商申请',
                content: `申请人：${userInfo.nickname || '未知用户'}\n店铺名称：${agentApplyInfo.shop_name}\n联系人：${agentApplyInfo.shop_link_name}\n联系电话：${agentApplyInfo.shop_link_phone}\n请及时审核处理`,
                extras: {
                    type: 'agent_apply',
                    apply_id: agentApplyInfo.id,
                    user_id: agentApplyInfo.user_id,
                    shop_name: agentApplyInfo.shop_name,
                    shop_link_name: agentApplyInfo.shop_link_name,
                    shop_link_phone: agentApplyInfo.shop_link_phone,
                    nickname: userInfo.nickname || ''
                }
            };

            // 发送推送
            const result = await this.sendPushNotification(aliasList, message);

            if (result) {
                think.logger.info(`代理商申请通知发送成功，申请ID：${agentApplyInfo.id}，申请人：${userInfo.nickname}，通知管理员数量：${aliasList.length}`);
            } else {
                think.logger.error(`代理商申请通知发送失败，申请ID：${agentApplyInfo.id}`);
            }

            return result;
        } catch (error) {
            think.logger.error('发送代理商申请通知时出错:', error);
            return false;
        }
    }

    /**
     * 获取支付类型文本
     * @param {string} payType 支付类型
     * @returns {string} 支付类型文本
     */
    getPayTypeText(payType) {
        const payTypeMap = {
            'wx': '微信支付',
            'ali': '支付宝',
            'balance': '余额支付'
        };
        return payTypeMap[payType] || '未知支付方式';
    }

    /**
     * 批量发送推送（分批处理，避免一次发送过多）
     * @param {Array} aliasList 推送别名列表
     * @param {Object} message 消息内容
     * @param {number} batchSize 批次大小，默认100
     * @returns {Promise<boolean>} 发送结果
     */
    async sendBatchPushNotification(aliasList, message, batchSize = 100) {
        try {
            if (!Array.isArray(aliasList) || aliasList.length === 0) {
                return false;
            }

            // 分批发送
            const batches = [];
            for (let i = 0; i < aliasList.length; i += batchSize) {
                batches.push(aliasList.slice(i, i + batchSize));
            }

            const results = await Promise.all(
                batches.map(batch => this.sendPushNotification(batch, message))
            );

            // 检查是否所有批次都发送成功
            const allSuccess = results.every(result => result === true);

            if (allSuccess) {
                think.logger.info(`批量推送发送成功，总数：${aliasList.length}，批次数：${batches.length}`);
            } else {
                think.logger.warn(`批量推送部分失败，总数：${aliasList.length}，成功批次：${results.filter(r => r).length}/${batches.length}`);
            }

            return allSuccess;
        } catch (error) {
            think.logger.error('批量推送发送异常:', error);
            return false;
        }
    }
};