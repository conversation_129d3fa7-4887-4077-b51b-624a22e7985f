/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-15 00:42:58
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-14 00:41:55
 * @FilePath: /petshop-server/src/common/service/service.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const { throwError } = require('../config/utils');
const tencentcloud = require("tencentcloud-sdk-nodejs-sms")
const isProd = think.env === 'production';

const smsClient = tencentcloud.sms.v20210111.Client

const client = new smsClient({
  credential: {
  /* 为了保护密钥安全，建议将密钥设置在环境变量中或者配置文件中。
   * 硬编码密钥到代码中有可能随代码泄露而暴露，有安全隐患，并不推荐。
   * SecretId、SecretKey 查询: https://console.cloud.tencent.com/cam/capi */
    secretId: think.config('tencentSMS.secretId'),
    secretKey: think.config('tencentSMS.secretKey'),
  },
  /* 必填：地域信息，可以直接填写字符串ap-guangzhou，支持的地域列表参考 https://cloud.tencent.com/document/api/382/52071#.E5.9C.B0.E5.9F.9F.E5.88.97.E8.A1.A8 */
  region: "ap-guangzhou",
  /* 非必填:
   * 客户端配置对象，可以指定超时时间等配置 */
  profile: {
    /* SDK默认用TC3-HMAC-SHA256进行签名，非必要请不要修改这个字段 */
    signMethod: "HmacSHA256",
    httpProfile: {
      reqMethod: "POST", // 请求方法
      reqTimeout: 10, // 请求超时时间，默认60s
      /**
       * 指定接入地域域名，默认就近地域接入域名为 sms.tencentcloudapi.com ，也支持指定地域域名访问，例如广州地域的域名为 sms.ap-guangzhou.tencentcloudapi.com
       */
      endpoint: "sms.tencentcloudapi.com"
    },
  },
})
module.exports = class extends think.Service {
    constructor(ctx) {
      super(ctx);
      this.throwError = throwError; // 挂载 throwError 到 this 上
    }

    // 发送短信
    async sendSMS(templateId, templateParamSet, phoneNumberSet) {
      const params = {
        /* 短信应用ID: 短信SdkAppId在 [短信控制台] 添加应用后生成的实际SdkAppId，示例如1400006666 */
        // 应用 ID 可前往 [短信控制台](https://console.cloud.tencent.com/smsv2/app-manage) 查看
        SmsSdkAppId: "1400990632",
        /* 短信签名内容: 使用 UTF-8 编码，必须填写已审核通过的签名 */
        // 签名信息可前往 [国内短信](https://console.cloud.tencent.com/smsv2/csms-sign) 或 [国际/港澳台短信](https://console.cloud.tencent.com/smsv2/isms-sign) 的签名管理查看
        SignName: "唯优众宠",
        /* 模板 ID: 必须填写已审核通过的模板 ID */
        // 模板 ID 可前往 [国内短信](https://console.cloud.tencent.com/smsv2/csms-template) 或 [国际/港澳台短信](https://console.cloud.tencent.com/smsv2/isms-template) 的正文模板管理查看
        TemplateId: templateId,
        /* 模板参数: 模板参数的个数需要与 TemplateId 对应模板的变量个数保持一致，若无模板参数，则设置为空 */
        TemplateParamSet: templateParamSet,
        /* 下发手机号码，采用 e.164 标准，+[国家或地区码][手机号]
        * 示例如：+8613711112222， 其中前面有一个+号 ，86为国家码，13711112222为手机号，最多不要超过200个手机号*/
        PhoneNumberSet: phoneNumberSet,
        /* 用户的 session 内容（无需要可忽略）: 可以携带用户侧 ID 等上下文信息，server 会原样返回 */
        SessionContext: "",
        /* 短信码号扩展号（无需要可忽略）: 默认未开通，如需开通请联系 [腾讯云短信小助手] */
        ExtendCode: "",
        /* 国内短信无需填写该项；国际/港澳台短信已申请独立 SenderId 需要填写该字段，默认使用公共 SenderId，无需填写该字段。注：月度使用量达到指定量级可申请独立 SenderId 使用，详情请联系 [腾讯云短信小助手](https://cloud.tencent.com/document/product/382/3773#.E6.8A.80.E6.9C.AF.E4.BA.A4.E6.B5.81)。 */
        SenderId: "",
      }
      try {
        if (isProd) {
          const response = await client.SendSms(params);
        
          think.logger.info("发送验证码响应：", response);
        }

        return null;
      } catch (error) {
        return error
      }
    }
  };