/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-08 09:37:14
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-09 10:01:04
 * @FilePath: /petshop-server/src/common/config/alipay_callback.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * 支付宝回调处理中间件
 * 专门用于处理支付宝的回调请求
 */

module.exports = options => {
  return async (ctx, next) => {
    // 只处理支付宝回调路径
    if (ctx.path === '/api/pay/aliPayNotify') {
      // 记录原始请求信息
      think.logger.info('支付宝回调请求头:', ctx.request.header);
      think.logger.info('支付宝回调请求方法:', ctx.method);
      think.logger.info('支付宝回调内容类型:', ctx.request.type);
      
      // 确保请求体已解析
      if (ctx.request.body) {
        think.logger.info('支付宝回调请求体已解析:', ctx.request.body);
      } else {
        think.logger.warn('支付宝回调请求体未解析');
        
        // 如果请求体未被解析，尝试从原始请求中获取
        try {
          // 检查是否是 POST 请求
          if (ctx.method === 'POST') {
            // 尝试手动解析请求体
            const rawBody = await new Promise((resolve, reject) => {
              let data = '';
              ctx.req.on('data', chunk => {
                data += chunk;
              });
              ctx.req.on('end', () => {
                resolve(data);
              });
              ctx.req.on('error', err => {
                reject(err);
              });
            });
            
            // 如果是 application/x-www-form-urlencoded 格式
            if (ctx.is('application/x-www-form-urlencoded')) {
              const querystring = require('querystring');
              ctx.request.body = querystring.parse(rawBody);
              think.logger.info('支付宝回调请求体手动解析:', ctx.request.body);
            } else {
              think.logger.info('支付宝回调原始请求体:', rawBody);
            }
          }
        } catch (err) {
          think.logger.error('解析支付宝回调请求体失败:', err);
        }
      }
      
      // 如果是 GET 请求，记录查询参数
      if (ctx.method === 'GET') {
        think.logger.info('支付宝回调查询参数:', ctx.query);
      }
    }
    
    await next();
  };
};
