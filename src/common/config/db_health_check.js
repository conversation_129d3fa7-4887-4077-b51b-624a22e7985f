/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-06-21 10:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-05 00:05:26
 * @FilePath: /petshop-server/src/common/config/db_health_check.js
 * @Description: 数据库健康检查中间件
 */

module.exports = (options, app) => {
  return async (ctx, next) => {
    const startTime = Date.now();
    
    try {
      // 每10分钟检查一次数据库连接
      if (!global.__DB_HEALTH_CHECK || Date.now() - global.__DB_HEALTH_CHECK > 600000) {
        const dbStartTime = Date.now();
        await think.model().query('SELECT 1');
        const dbEndTime = Date.now();
        
        // 记录数据库响应时间
        if (dbEndTime - dbStartTime > 1000) {
          think.logger.warn(`数据库响应时间过长: ${dbEndTime - dbStartTime}ms`);
        }
        
        global.__DB_HEALTH_CHECK = Date.now();
        think.logger.info(`数据库健康检查通过，响应时间: ${dbEndTime - dbStartTime}ms`);
      }
      
      await next();
      
      // 记录请求总耗时
      const totalTime = Date.now() - startTime;
      if (totalTime > 5000) {
        think.logger.warn(`请求处理时间过长: ${totalTime}ms, URL: ${ctx.path}`);
      }
      
    } catch (err) {
      if (err.code === 'PROTOCOL_CONNECTION_LOST' || err.code === 'ECONNREFUSED') {
        think.logger.error('数据库连接丢失，尝试重连');
        // 重置连接池
        think.model().close();
      } else if (err.code === 'ER_LOCK_WAIT_TIMEOUT') {
        think.logger.error('数据库锁等待超时');
      } else if (err.code === 'ER_LOCK_DEADLOCK') {
        think.logger.error('数据库死锁');
      }
      throw err;
    }
  };
};