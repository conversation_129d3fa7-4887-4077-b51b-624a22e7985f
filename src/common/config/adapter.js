/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-21 15:43:48
 * @LastEditors: Haki <EMAIL>
 * @LastEditTime: 2025-04-13 16:58:37
 * @FilePath: /petshop-server/src/common/config/adapter.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const fileCache = require('think-cache-file');
const {
    Console,
    File,
    DateFile
} = require('think-logger3');
const path = require('path');
const database = require('./database.js');
const redis = require('./redis.js');
const nunjucks = require('think-view-nunjucks');
const isDev = think.env === 'development';
/**
 * cache adapter config
 * @type {Object}
 */
exports.cache = {
    type: 'redis',
    redis: redis
};
/**
 * model adapter config
 * @type {Object}
 */
exports.model = {
    type: 'mysql',
    common: {
        logConnect: isDev,
        logSql: isDev,
        logger: msg => think.logger.info(msg)
    },
    mysql: database
};
/**
 * logger adapter config
 * @type {Object}
 */
exports.logger = {
    type: isDev ? 'console' : 'dateFile',
    console: {
        handle: Console
    },
    dateFile: {
        handle: DateFile,
        level: 'ALL',
        absolute: true,
        pattern: '-yyyy-MM-dd',
        alwaysIncludePattern: true,
        filename: path.join(path.dirname(think.ROOT_PATH), 'logs/app.log')
    }
};
exports.view = {
    type: 'nunjucks', // 这里指定默认的模板引擎是 nunjucks
    common: {
        viewPath: path.join(think.ROOT_PATH, 'view'), //模板文件的根目录
        sep: '_', //Controller 与 Action 之间的连接符
        extname: '.html' //模板文件扩展名
    },
    nunjucks: {
        handle: nunjucks,
        beforeRender: () => {}, // 模板渲染预处理
        options: { // 模板引擎额外的配置参数
        }
    }
}