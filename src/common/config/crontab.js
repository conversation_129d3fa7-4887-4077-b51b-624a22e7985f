/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-25 23:39:56
 * @FilePath: /petshop-server/src/common/config/crontab.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const path = require('path');
module.exports = [
	// 重置广告定时任务，现在停掉该任务；
	{
		interval: '60s',
		enable: false,
		immediate: true,
		handle: "crontab/timetask"
	},
]
