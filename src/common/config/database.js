/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2025-02-22 10:59:25
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-21 00:49:34
 * @FilePath: /hioshop-server/src/common/config/database.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const mysql = require('think-model-mysql');
const isProd = think.env === 'production';

const databaseDev = {
    database: 'hiolabsDB',
    prefix: 'petshop_',
    encoding: 'utf8mb4',
    host: '***************',
    port: '3383',
    user: 'root',
    password: '123456',
    dateStrings: true,
    // 添加连接池配置
    connectionLimit: 10,      // 最大连接数
    acquireTimeout: 10000,    // 获取连接超时时间
    waitForConnections: true, // 是否等待连接
    queueLimit: 0,            // 队列限制
    connectTimeout: 10000,    // 连接超时
    idleTimeout: 60000        // 空闲超时
};

const databaseProd = {
    database: 'petshop',
    prefix: 'petshop_',
    encoding: 'utf8mb4',
    host: 'sh-cdb-68embt5y.sql.tencentcdb.com',
    port: '20855',
    user: 'root',
    password: 'Haki19283755.',
    dateStrings: true,
    // 添加连接池配置
    connectionLimit: 20,      // 最大连接数（生产环境增加）
    acquireTimeout: 10000,    // 获取连接超时时间
    waitForConnections: true, // 是否等待连接
    queueLimit: 0,            // 队列限制
    connectTimeout: 10000,    // 连接超时
    idleTimeout: 60000,       // 空闲超时
    // 增加事务相关配置
    multipleStatements: true, // 支持多条语句
    timeout: 60000,           // 查询超时
    // 增加重连配置
    reconnect: true,          // 自动重连
    charset: 'utf8mb4'        // 字符集
};

const database = isProd ? databaseProd : databaseDev;
module.exports = {
    handle: mysql,
    ...database
};
