/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-17 00:07:50
 * @FilePath: /petshop-server/src/common/config/config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// default config
module.exports = {
    default_module: 'api',
    port: 8360, //服务端口，可自定义
    weixin: {
        appid: 'wx3a6ab4334aa1402b', // 小程序 appid
        secret: '6b43b2db09b77d25d2e6234379ae849d', // 小程序密钥
        mch_id: '15988888888', // 商户帐号ID
        partner_key: 'lwibFgL0RiYa0EZ9PLO5scu2rM9K2TfL', // 微信支付密钥
        notify_url: 'https://www.您的域名.com/api/pay/notify' // 微信支付异步通知
    },
    tencentCos: {
        
    },
    templateId:{
      deliveryId:'w6AMCJ0nVWTsFasdasdgnlNlmCf9TTDmG6_U' // 模板id。在订阅消息里设置好后就可以得到
    },
    kuaidi100:{
        key: "vtOhgeEY7444", // 快递100的企业授权码key
        secret: "17aa8a80d75548b3a72ba5700d7808ad", // 快递100的企业授权码secret
        customer: "DBC2E60128338BD2552B9DB70B393B13",
        siid: "KX100L3AFE8554D8F0",
        printOrderUrl: "https://api.kuaidi100.com/label/order",
        queryExpressUrl: "https://poll.kuaidi100.com/poll/query.do",
        identifyExpressNum: "http://www.kuaidi100.com/autonumber/auto", // 快递单号识别
    },
    tencentSMS: {
        secretId: "AKIDJSM0OWOzLRGp8B0cy44zf7KLAoMp8B0b",
        secretKey: "QZS5SZXFhtpVv5zf6JHzRdZFfpiLipgN",
    }
};
