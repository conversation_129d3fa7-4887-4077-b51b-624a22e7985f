/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-09 09:47:43
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-15 23:48:29
 * @FilePath: /petshop-server/src/common/config/middleware.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const path = require('path');
const isDev = think.env === 'development';
const kcors = require('kcors');
const requestLogger = require('./request_logger');
const authUser = require('./auth_user');
const errorHandle = require('./error_handle');
const alipayCallback = require('./alipay_callback');
const dbHealthCheck = require('./db_health_check');

module.exports = [
// 错误跟踪
// ​作用：捕获异常并返回格式化错误信息。开发环境 (debug: true) 显示详细堆栈。
// ​位置：在可能抛出错误的中间件之前（如路由、逻辑层）。
{
  handle: 'trace',
  enable: !think.isCli,
  options: {
      debug: isDev
  }
},
{
  handle: errorHandle,
  enable: true
},
// 其他中间件
async (ctx, next) => {
    if (ctx.path === '/favicon.ico') {
      ctx.status = 204;
    } else {
      await next();
    }
},
{
    handle: kcors, // 处理跨域
    options: {}
},

{
    handle: 'meta',
    options: {
        // 是否打印请求路径和响应时间和状态，因为添加了 request_logger 中间件，所以这里就不需要了
        logRequest: false,
        sendResponseTime: false
    }
},
// 添加 auth_user 中间件
{
  handle: authUser, //
  enable: true, // 开启中间件
  ignore: [
    '/api/auth/loginByMobile',
    '/api/auth/loginByWeixin',
    '/api/auth/sendCode',

    '/admin/auth/login',

    '/api/pay/aliPayNotify',
    '/api/pay/wxPayNotify',
    '/api/pay/wxPayRefundNotify',
    
    "/api/pay/aliPayRecharge",
    "/api/pay/weixinPayRecharge",
  ]
},
// 支付宝回调处理中间件
{
  handle: alipayCallback,
  enable: true,
  options: {}
},
// 全局日志打印
{
  handle: requestLogger,
  enable: true, // 开启中间件
  options: {}, // 可传递配置参数
  // match: '/', // 匹配所有路由
  // ignore: [] // 忽略的路由（如健康检查接口）
},
{
    handle: 'payload',
    options: {
        // 增强 payload 中间件的配置
        formLimit: '2mb',
        jsonLimit: '2mb',
        textLimit: '2mb',
        // 支持的内容类型
        extTypes: {
            json: ['application/json'],
            form: ['application/x-www-form-urlencoded'],
            text: ['text/plain']
        }
    }
}, {
    handle: 'router',
    options: {
        defaultModule: 'api',
        defaultController: 'index',
        defaultAction: 'index'
    }
},
'logic',
'controller',
// 添加数据库健康检查中间件
{
  handle: dbHealthCheck,
  enable: true,
  options: {}
}
];
