/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2025-02-22 10:59:25
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-13 17:52:33
 * @FilePath: /hioshop-server/src/common/config/database.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

const redisCache = require('think-cache-redis');
const isProd = think.env === 'production';

const redisDev = {
  host: '***************',
  port: 4379,
  password: 'lige@666',
  db: 1,
  timeout: 3000
};
const redisProd = {
  host: '*************',
  port: 4379,
  password: '<PERSON><PERSON>19283755.',
  db: 1,
  timeout: 3000
};

const redis = isProd ? redisProd : redisDev;
module.exports = {
  handle: redisCache,
  ...redis
};