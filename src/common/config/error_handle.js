/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-26 18:04:22
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-08 10:12:46
 * @FilePath: /petshop-server/src/common/config/error_handle.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// src/middleware/error_handler.js
const isDev = think.env === 'development';
module.exports = (options, app) => {
  return async (ctx, next) => {
    try {
      await next();
    } catch (e) {
      const code = e.code;
      const message = e.message || '系统繁忙，请稍后再试';

      // 避免和 request_logger 重复记录错误，只补充 trace
      think.logger.error(`
        ========== [GlobalErrorHandler] ==========
        Time: ${new Date().toISOString()}
        URL: ${ctx.method} ${ctx.url}
        Status: ${e.status}
        headers: ${JSON.stringify(ctx.headers)},
        query: ${JSON.stringify(ctx.query)},
        requestBody: ${JSON.stringify(ctx.request.body)},
        status: ctx.status,
        Error: ${e.name} - ${e.message}
        Stack:
        ${e.stack}
        ==========================================
      `);

      ctx.status = 200; // 保持统一响应结构
      ctx.body = {
        errno: code,
        errmsg: message,
        data: null
      };
    }
  };
};