/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-26 11:04:03
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-04-28 09:49:30
 * @FilePath: /petshop-server/src/common/middleware/request_logger.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

const { think } = require("thinkjs");

// src/middleware/request_logger.js
module.exports = (options, app) => {
  return async (ctx, next) => {
    const startTime = Date.now();
    let resultCode = 0;
    let resultMessage = '';
    try {
      await next();
    } catch (e) {
      resultCode = e.code;
      resultMessage = e.message || '系统繁忙，请稍后再试';
      if (e.code != -1) {
        throw e; // 抛给 error_handler 中间件统一处理响应
      }else{
        ctx.status = 200; // 保持统一响应结构
        ctx.body = {
          errno: resultCode,
          errmsg: resultMessage,
          data: null
        };
      }
    }

    const cost = Date.now() - startTime;
    ctx.set('X-Response-Time', `${cost}ms`);

    // 响应日志也可以结构化，便于机器检索
    const logData = {
      url: ctx.url,
      headers: JSON.stringify(ctx.headers),
      query: ctx.query,
      requestBody: JSON.stringify(ctx.request.body),
      status: ctx.status,
      cost: `${cost}ms`,
      responseBody: JSON.stringify(ctx.body)
    };

    if (resultCode == 0) {
      think.logger.info('[RequestSuccess]', logData);
    } else {
      think.logger.warn('[RequestError]', logData);
    }
  };
};