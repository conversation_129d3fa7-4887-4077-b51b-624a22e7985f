/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-04-18 18:50:25
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-28 00:55:59
 * @FilePath: /petshop-server/src/common/config/global.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = {
  /**
   * 抛出带有业务错误码的错误。
   * @param {string} message - 错误信息。
   * @param {number} [errno=-1] - 业务错误码，默认值为-1。
   * @returns {never} 直接抛出错误，不会返回。
   */
  throwError(code = 500, msg = '系统错误') {
    const err = new Error(msg);
    err.code = code;
    return err;
  },
};