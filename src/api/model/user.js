/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-21 17:50:49
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-23 22:13:12
 * @FilePath: /petshop-server/src/api/model/user.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = class extends think.Model {
  /**
   * 使用手机号注册新用户
   */
  async registerByMobileAction(mobile, clientIp) {
    // nickname 手机号中间四位****掩码
    const nickname = mobile.substr(0, 3) + "****" + mobile.substr(7);
    let currentTime = parseInt(new Date().getTime() / 1000);
    // password 使用 mobile MD5
    const password = think.md5(mobile);
    
    // 注册用户
    const userId = await this.model("user").add({
      username: mobile,
      password: password,
      register_time: currentTime,
      register_ip: clientIp,
      last_login_time: currentTime,
      last_login_ip: clientIp,
      mobile: mobile,
      nickname: nickname,
      avatar:'',
    });

    return userId;
  }

  // 更新用户后自动清理缓存
  async afterUpdate(data, options) {  
    // 获取所有被更新的用户 ID
    const affectedIds = await this.model('user')
      .field('id')
      .where(options.where)
      .select();
  
    // 批量清理缓存
    affectedIds.forEach(async ({ id }) => {
      await this.cache(`user_${id}`, null);
    });
  }
};