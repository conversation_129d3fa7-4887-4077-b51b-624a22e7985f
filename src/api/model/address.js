const { think } = require("thinkjs");

module.exports = class extends think.Model {
  /*
   * 获取当前地址的物流配置信息
   * @param userId, addressId checkedGoodsList
   * @returns {Promise.<*>}
   */
  async checkedAddress(addressId, userId, checkedGoodsList, userAgent) {
    if (think.isEmpty(userId)) return null;

    const AddressModel = this.model('address');
    const RegionModel = this.model('region');
    const FreightTemplateModel = this.model('freight_template');
    const FreightTemplateDetailModel = this.model('freight_template_detail');
    const FreightTemplateGroupModel = this.model('freight_template_group');
  
    const findAddWhere = {
      user_id: userId,
      is_delete: 0,
      ...(addressId == '' || addressId == 0 ? { is_default: 1 } : { id: addressId })
    };
  
    const checkedAddress = await AddressModel.where(findAddWhere).find();
    if (think.isEmpty(checkedAddress)) return null;
  
    // 拼接地址名称
    const [provinceName, cityName, districtName] = await Promise.all([
      RegionModel.getRegionName(checkedAddress.province_id),
      RegionModel.getRegionName(checkedAddress.city_id),
      RegionModel.getRegionName(checkedAddress.district_id)
    ]);
  
    checkedAddress.province_name = provinceName;
    checkedAddress.city_name = cityName;
    checkedAddress.district_name = districtName;
    checkedAddress.full_region = `${provinceName}${cityName}${districtName}`;
  
    // 模板缓存
    const allTemplates = await FreightTemplateModel.where({ is_delete: 0 }).select();
    const templateMap = new Map(allTemplates.map(t => [t.id, t]));
  
    // 分组聚合每个模板的商品项，同时按商品运费模式分组
    const freightDataMap = new Map();
    const freeShippingGoods = []; // 包邮商品列表
    
    for (const cartItem of checkedGoodsList) {
      // 检查商品的运费模式
      if (cartItem.freight_mode === 1) {
        // 一件包邮商品，直接加入包邮列表
        freeShippingGoods.push(cartItem);
        continue;
      }
      
      // 按模板计算运费的商品
      const tplId = cartItem.freight_template_id;
      if (!freightDataMap.has(tplId)) {
        let setObj = {
          id: tplId,
          number: 0,
          money: 0,
          goods_weight: 0,
        }
        if (!think.isEmpty(templateMap.get(tplId))) {
          setObj.template_freight_type = templateMap.get(tplId).freight_type ? templateMap.get(tplId).freight_type : 0;
        }
        freightDataMap.set(tplId, setObj);
      }
      const item = freightDataMap.get(tplId);
      item.number += cartItem.buy_number;
      item.money += cartItem.buy_number * cartItem.retail_price;
      item.goods_weight += cartItem.buy_number * cartItem.goods_weight;
    }
  
    let maxFreightPrice = 0;
    
    // 计算按模板收费的运费
    for (const item of freightDataMap.values()) {
      if (item.number === 0) continue;

      const tempGroup = await FreightTemplateGroupModel.where({
        template_id: item.id,
        is_delete: 0
      }).select();

      if (think.isEmpty(tempGroup)) {
        continue;
      }
  
      const freightDetail = await FreightTemplateDetailModel.where({
        template_id: item.id,
        area: checkedAddress.province_id,
        is_delete: 0
      }).find();

      let groupData;
      if (think.isEmpty(freightDetail)) {
        // 代表没有设置地区运费模板，则使用默认的 group 模版即可
        groupData = tempGroup.find(v => v.is_default === 1)
        if (think.isEmpty(groupData)) {
          groupData = tempGroup[0];
        }
      }else{
        groupData = tempGroup.find(v => v.id === freightDetail.group_id);
      }
  
      const templateInfo = templateMap.get(item.id);
      const templateFreightType = !think.isEmpty(templateInfo) ? (templateInfo.freight_type || 0) : 0;
      
      // 根据模板运费类型调用不同的计算方法
      let freightPrice;
      if (templateFreightType === 0) {
        freightPrice = await this.calcFreightPriceByNumber(item, groupData);
      } else {
        freightPrice = await this.calcFreightPriceByWeight(item, groupData);
      }
      
      maxFreightPrice = Math.max(maxFreightPrice, freightPrice);
    }
    
    // 如果有包邮商品，检查是否满足包邮条件
    if (freeShippingGoods.length > 0) {
      const hasFreeShipping = await this.checkFreeShippingCondition(freeShippingGoods, checkedAddress);
      if (hasFreeShipping) {
        maxFreightPrice = 0; // 满足包邮条件，运费为0
      }
    }
  
    checkedAddress.freight_price = maxFreightPrice;
    return checkedAddress;
  }

  /**
   * 检查包邮条件
   * @param freeShippingGoods 包邮商品列表
   * @param address 收货地址
   * @returns {Promise<boolean>}
   */
  async checkFreeShippingCondition(freeShippingGoods, address) {
    // 这里可以根据业务需求设置包邮条件
    // 例如：包邮商品总数量达到某个值，或者总金额达到某个值
    
    const totalQuantity = freeShippingGoods.reduce((sum, item) => sum + item.buy_number, 0);
    const totalAmount = freeShippingGoods.reduce((sum, item) => sum + (item.buy_number * item.retail_price), 0);
    
    // 示例：包邮商品总数量 >= 1 或者总金额 >= 99 元时包邮
    // 您可以根据实际业务需求调整这些条件
    const freeShippingQuantity = 1; // 包邮数量条件
    const freeShippingAmount = 99; // 包邮金额条件（元）
    
    return (
      totalQuantity >= freeShippingQuantity 
      // || totalAmount >= freeShippingAmount
    );
  }

  /**
   * 按数量计算运费 (模板 freight_type = 0)
   */
  async calcFreightPriceByNumber(item, groupData) {
    if (!groupData) return 0;
  
    const {
      start = 0,
      start_fee = 0,
      add_fee = 0,
      free_by_number = 0,
      free_by_money = 0
    } = groupData;
  
    let freightPrice = 0;
    const number = item.number;
  
    if (number > start) {
      freightPrice = start_fee + (number - start) * add_fee;
    } else {
      freightPrice = start_fee;
    }
  
    // 检查是否满足包邮条件
    if ((free_by_number && number >= free_by_number) || (free_by_money && item.money >= free_by_money)) {
      freightPrice = 0;
    }
  
    return freightPrice;
  }

  /**
   * 按重量计算运费 (模板 freight_type = 1)
   */
  async calcFreightPriceByWeight(item, groupData) {
    if (!groupData) return 0;
  
    const {
      start = 0,
      start_fee = 0,
      add_fee = 0,
      free_by_number = 0,
      free_by_money = 0
    } = groupData;
  
    let freightPrice = 0;
    const weight = item.goods_weight; // 重量（克）
  
    if (weight > start) {
      freightPrice = start_fee + Math.ceil(((weight - start)/1000)) * add_fee;
    } else {
      freightPrice = start_fee;
    }
  
    // 检查是否满足包邮条件
    if ((free_by_number && item.number >= free_by_number) || (free_by_money && item.money >= free_by_money)) {
      freightPrice = 0;
    }
  
    return freightPrice;
  }

  /**
   * 运费计算方法封装（保留原方法以兼容旧代码）
   * @deprecated 建议使用 calcFreightPriceByNumber 或 calcFreightPriceByWeight
   */
  async calcFreightPrice(item, groupData, freight_type = 0) {
    if (freight_type === 0) {
      return await this.calcFreightPriceByNumber(item, groupData);
    } else {
      return await this.calcFreightPriceByWeight(item, groupData);
    }
  }
};
