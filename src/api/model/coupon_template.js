/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-01 00:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-22 00:18:05
 * @FilePath: /petshop-server/src/api/model/coupon_template.js
 * @Description: 优惠券模板模型
 */
module.exports = class extends think.Model {
  /**
   * 获取新用户专属的优惠券模板
   */
  async getNewUserCoupons() {
    return await this.where({
      is_for_new_user: 1,
      status: 1,
      is_delete: 0
    }).select();
  }

  /**
   * 检查优惠券模板是否还有剩余数量
   */
  async checkRemainingCount(templateId, session = null) {
    // 使用传入的 session 或当前实例
    const model = session ? this.db(session) : this;
    const userCouponModel = session ? this.model('user_coupon').db(session) : this.model('user_coupon');
    
    const template = await model.where({
      id: templateId,
      status: 1,
      is_delete: 0
    }).find();

    if (think.isEmpty(template)) {
      return false;
    }

    // 如果 total_count 为 0，表示不限制数量
    if (template.total_count === 0) {
      return true;
    }

    // 统计已发放的数量
    const usedCount = await userCouponModel.where({
      coupon_template_id: templateId,
      is_delete: 0
    }).count();

    return usedCount < template.total_count;
  }
}; 