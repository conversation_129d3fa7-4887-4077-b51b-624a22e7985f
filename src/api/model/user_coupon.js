/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-01 00:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-22 00:18:37
 * @FilePath: /petshop-server/src/api/model/user_coupon.js
 * @Description: 用户优惠券模型
 */
const moment = require('moment');
module.exports = class extends think.Model {
  /**
   * 生成优惠券码
   */
  generateCouponCode() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 6);
    return `CP${timestamp}${random}`.toUpperCase();
  }

  /**
   * 为用户发放优惠券
   */
  async issueCouponToUser(userId, templateId, remark = '', session = null) {
    // 使用传入的 session 或当前实例
    const model = session ? this.db(session) : this;
    const couponTemplateModel = session ? this.model('coupon_template').db(session) : this.model('coupon_template');
    
    const template = await couponTemplateModel.where({
      id: templateId,
      status: 1,
      is_delete: 0
    }).find();

    if (think.isEmpty(template)) {
      throw new Error('优惠券模板不存在或已停用');
    }

    // 检查是否已经领取过该模板的优惠券
    const existingCoupon = await model.where({
      user_id: userId,
      coupon_template_id: templateId,
      is_delete: 0
    }).find();

    if (!think.isEmpty(existingCoupon)) {
      throw new Error('您已经领取过该优惠券');
    }

    // 检查剩余数量
    const hasRemaining = await couponTemplateModel.checkRemainingCount(templateId);
    if (!hasRemaining) {
      throw new Error('优惠券已发放完毕');
    }

    // 计算有效期
    const now = moment();
    const validStart = now.format('YYYY-MM-DD HH:mm:ss');
    const validEnd = now.add(template.valid_hour, 'hours').format('YYYY-MM-DD HH:mm:ss');

    // 生成优惠券码
    const couponCode = this.generateCouponCode();

    // 创建用户优惠券记录
    const couponData = {
      user_id: userId,
      coupon_template_id: templateId,
      coupon_code: couponCode,
      is_used: 0,
      valid_start: validStart,
      valid_end: validEnd,
      remark: remark || `新用户专享${template.name}`,
      is_delete: 0
    };

    const couponId = await model.add(couponData);
    return couponId;
  }

  /**
   * 为新用户自动发放优惠券
   */
  async autoIssueNewUserCoupons(userId, session = null) {
    try {
      // 使用传入的 session 或当前实例
      const model = session ? this.db(session) : this;
      const couponTemplateModel = session ? this.model('coupon_template').db(session) : this.model('coupon_template');
      
      // 获取新用户专属的优惠券模板
      const newUserTemplates = await couponTemplateModel.getNewUserCoupons();
      
      if (think.isEmpty(newUserTemplates)) {
        return []; // 没有新用户专属优惠券
      }

      const issuedCoupons = [];

      for (const template of newUserTemplates) {
        try {
          // 检查是否已经领取过
          const existingCoupon = await model.where({
            user_id: userId,
            coupon_template_id: template.id,
            is_delete: 0
          }).find();

          if (!think.isEmpty(existingCoupon)) {
            continue; // 已经领取过，跳过
          }

          // 检查剩余数量
          const hasRemaining = await couponTemplateModel.checkRemainingCount(template.id, session);
          if (!hasRemaining) {
            continue; // 已发放完毕，跳过
          }

          // 发放优惠券
          const couponId = await model.issueCouponToUser(userId, template.id, `新用户专享${template.name}`, session);
          issuedCoupons.push({
            template_id: template.id,
            template_name: template.name,
            coupon_id: couponId
          });

        } catch (error) {
          // 单个优惠券发放失败，记录日志但不影响其他优惠券
          think.logger.error(`为用户 ${userId} 发放优惠券模板 ${template.id} 失败:`, error.message);
        }
      }

      return issuedCoupons;
    } catch (error) {
      think.logger.error(`为新用户 ${userId} 自动发放优惠券失败:`, error.message);
      return [];
    }
  }

  /**
   * 获取用户的有效优惠券列表
   */
  async getUserValidCoupons(userId) {
    const now = moment().format('YYYY-MM-DD HH:mm:ss');
    
    return await this.alias('uc')
      .field('uc.*, ct.name as template_name, ct.type, ct.discount, ct.min_amount')
      .join({
        table: 'coupon_template',
        join: 'left',
        as: 'ct',
        on: ['ct.id', 'uc.coupon_template_id']
      })
      .where({
        'uc.user_id': userId,
        'uc.is_used': 0,
        'uc.is_delete': 0,
        'uc.valid_start': ['<=', now],
        'uc.valid_end': ['>=', now]
      })
      .order('uc.create_time DESC')
      .select();
  }
}; 