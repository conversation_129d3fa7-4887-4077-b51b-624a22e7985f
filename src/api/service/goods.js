/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-04-18 16:28:17
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-05 01:13:09
 * @FilePath: /petshop-server/src/api/service/goods.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// src/service/goods.js
const Base = require('../../common/service/base.js');
module.exports = class extends Base {
  /**
   * 商品库存校验 + 总价计算
   */
  async _checkGoodsAndCalcTotal(goodsList, goodsModel, userAgent) {
    const checkPudList = await goodsModel.checkProduct(goodsList);
    if (checkPudList.length !== goodsList.length) {
      throw this.throwError(-1, '部分商品已下架或库存不足');
    }

    let goodsTotalPrice = 0;
    for (const item of checkPudList) {
      const match = goodsList.find(g => g.goodsId == item.goods_id && g.productId == item.product_id);
      if (!match || !match.number) throw this.throwError(-1, '商品信息有误');

      const unitPrice = userAgent && !think.isEmpty(item.agent_price) ? item.agent_price : item.retail_price;
      goodsTotalPrice += parseFloat(unitPrice) * parseInt(match.number);
      item.buy_number = match.number;

      if (userAgent && item.agent_price) {
        item.retail_price = item.agent_price;
      }
    }

    return { checkPudList, goodsTotalPrice };
  }

  /**
   * 地址获取逻辑封装
   */
  async _getValidAddress(addressId, userId, addressModel) {
    let address = null;

    if (addressId) {
      address = await addressModel.where({ id: addressId, user_id: userId, is_delete: 0 }).find();
    } else {
      address = await addressModel.where({ user_id: userId, is_default: 1, is_delete: 0 }).find();
    }

    if (think.isEmpty(address)) {
      throw this.throwError(-1, addressId ? '收货地址有误' : '请选择收货地址');
    }

    return address;
  }

  /**
   * 是否只有包邮商品
   */
  _isOnlyFreeShipping(goodsList) {
    const free = goodsList.filter(i => i.freight_mode === 1);
    const normal = goodsList.filter(i => i.freight_mode !== 1);
    return free.length > 0 && normal.length === 0;
  }

  /**
   * 检查商品是否可下单并计算价格信息
   */
  async checkGoodsOrder(goodsList, addressId, userId, userAgent, goodsModel, addressModel, selectedFreightTemplateId = null) {
    let freightPrice = 0;
    const { checkPudList, goodsTotalPrice } = await this._checkGoodsAndCalcTotal(goodsList, goodsModel, userAgent);
    const onlyFreeShippingGoods = this._isOnlyFreeShipping(checkPudList);

    // 运费模板分配
    if (selectedFreightTemplateId !== null) {
      if (selectedFreightTemplateId === 0) {
        if (!onlyFreeShippingGoods) throw this.throwError(-1, '当前订单不支持包邮，请选择其他运费模板');
      } else {
        const selectedTemplate = await this.model('freight_template').where({
          id: selectedFreightTemplateId,
          is_delete: 0,
        }).find();

        if (think.isEmpty(selectedTemplate)) {
          throw this.throwError(-1, '指定的运费模板不可用');
        }

        for (const item of checkPudList) {
          if (item.freight_mode !== 1) {
            item.freight_template_id = selectedFreightTemplateId;
          }
        }
      }
    } else if (!onlyFreeShippingGoods) {
      const defaultTemplate = await this.model('freight_template').where({
        is_delete: 0,
        freight_use_type: ['IN', [userAgent ? 2 : 1, 3]]
      }).find();

      if (!think.isEmpty(defaultTemplate)) {
        for (const item of checkPudList) {
          if (item.freight_mode !== 1 && item.freight_template_id === 0) {
            item.freight_template_id = defaultTemplate.id;
          }
        }
      }
    }

    const address = await this._getValidAddress(addressId, userId, addressModel);

    const addressForCalc = selectedFreightTemplateId === 0
      ? checkPudList.filter(i => i.freight_mode === 1)
      : checkPudList;

    const checkedAddress = await addressModel.checkedAddress(address.id, userId, addressForCalc, userAgent);
    if (!think.isEmpty(checkedAddress)) {
      freightPrice = checkedAddress.freight_price;
    }

    return {
      goodsTotalPrice: goodsTotalPrice.toFixed(2),
      freightPrice: freightPrice.toFixed(2),
      checkedGoodsList: checkPudList,
      checkedAddress
    };
  }

  /**
   * 获取所有可用的运费模板并计算运费（按运费从低到高排序）
   */
  async getAvailableFreightTemplates(goodsList, addressId, userId, userAgent, goodsModel, addressModel) {
    const { checkPudList, goodsTotalPrice } = await this._checkGoodsAndCalcTotal(goodsList, goodsModel, userAgent);
    const address = await this._getValidAddress(addressId, userId, addressModel);

    const onlyFreeShippingGoods = this._isOnlyFreeShipping(checkPudList);
    const hasExclusiveTemplate = checkPudList.some(item => item.freight_template_id > 0);

    let allFreightTemplates = [];
    if (hasExclusiveTemplate) {
      const ids = [...new Set(checkPudList.filter(i => i.freight_template_id > 0).map(i => i.freight_template_id))];
      allFreightTemplates = await this.model('freight_template').where({ id: ['IN', ids], is_delete: 0 }).select();
    } else {
      allFreightTemplates = await this.model('freight_template').where({
        is_delete: 0,
        freight_use_type: ['IN', [userAgent ? 2 : 1, 3]]
      }).select();
    }

    if (think.isEmpty(allFreightTemplates)) {
      throw this.throwError(-1, '暂无可用的运费模板');
    }

    const freightTemplateResults = [];

    for (const template of allFreightTemplates) {
      try {
        const tempCheckList = checkPudList.map(item => ({
          ...item,
          freight_mode: onlyFreeShippingGoods ? 0 : item.freight_mode,
          freight_template_id: hasExclusiveTemplate
            ? (item.freight_template_id > 0 ? item.freight_template_id : template.id)
            : (item.freight_template_id === 0 ? template.id : item.freight_template_id)
        }));

        const tempAddress = await addressModel.checkedAddress(address.id, userId, tempCheckList, userAgent);
        if (!think.isEmpty(tempAddress)) {
          freightTemplateResults.push({
            template_id: template.id,
            template_name: template.name,
            freight_type: template.freight_type,
            freight_use_type: template.freight_use_type,
            freight_price: parseFloat(tempAddress.freight_price),
            description: template.description || ''
          });
        }
      } catch (err) {
        think.logger.warn(`运费模板 ${template.id} 运费计算失败: ${err.message}`);
      }
    }

    // 如果全部商品包邮，插入包邮模板
    if (onlyFreeShippingGoods) {
      const tempAddress = await addressModel.checkedAddress(address.id, userId, checkPudList, userAgent);
      if (!think.isEmpty(tempAddress) && tempAddress.freight_price === 0) {
        freightTemplateResults.unshift({
          template_id: 0,
          template_name: '一件包邮（猫粮/犬粮/猫砂/怡享专用）',
          freight_type: 0,
          freight_use_type: 3,
          freight_price: 0,
          description: ''
        });
      }
    }

    // 附加完整地址信息
    const addressFull = await addressModel.getFullAddress(address.id, userId);
    address.province_name = addressFull.province_name;
    address.city_name = addressFull.city_name;
    address.district_name = addressFull.district_name;
    address.full_region = addressFull.full_region;

    freightTemplateResults.sort((a, b) => a.freight_price - b.freight_price);

    return {
      goodsTotalPrice: goodsTotalPrice.toFixed(2),
      checkedGoodsList: checkPudList,
      checkedAddress: address,
      freightTemplates: freightTemplateResults
    };
  }
};
