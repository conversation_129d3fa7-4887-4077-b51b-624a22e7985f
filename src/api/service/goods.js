/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-04-18 16:28:17
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-22 17:54:48
 * @FilePath: /petshop-server/src/api/service/goods.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// src/service/goods.js
const Base = require('../../common/service/base.js');
module.exports = class extends Base {
  /**
   * 检查商品是否可下单并计算价格信息
   * @param {Array} goodsList
   * @param {String|Number} addressId
   * @param {String|Number} userId
   * @param {String} userAgent
   * @returns {Object}
   */
  async checkGoodsOrder(goodsList, addressId, userId, userAgent, goodsModel, addressModel) {
    let goodsTotalPrice = 0;
    let freightPrice = 0;

    try {
      // 检查商品库存等信息
      const checkPudList = await goodsModel.checkProduct(goodsList);
      if (checkPudList.length !== goodsList.length) {
        throw this.throwError(-1, '部分商品已下架或库存不足');
      }
      
      // 判断 checkPudList 中的 freight_template_id 是否为 0，
      // 如果为 0，则代表没有配置专属模版，则使用全部生效的模版 并且只查出一条（id 最小的）
      const freightTemplate = await this.model('freight_template').where({
        is_delete: 0,
        freight_use_type: ['IN', [userAgent ? 2 : 1, 3]]
      }).find();
      
      if (!think.isEmpty(freightTemplate)) {
        for (let item of checkPudList) {
          if (item.freight_template_id === 0) {
            item.freight_template_id = freightTemplate.id || 0;
          }
        }
      }

      // 计算商品总金额
      for (let item of checkPudList) {
        let num = goodsList.filter(goods => goods.goodsId == item.goods_id && goods.productId == item.product_id);
        if (num.length > 0 && num[0].number) {
          if (userAgent && !think.isEmpty(item.agent_price)) {
            goodsTotalPrice += parseFloat(num[0].number) * parseFloat(item.agent_price);
          } else {
            goodsTotalPrice += parseFloat(num[0].number) * parseFloat(item.retail_price);
          }

          item.buy_number = num[0].number;
        }else{
          throw this.throwError(-1, '商品信息有误');
        }
      }
  
      // 查询物流运费
      let checkAdd = null;
      if (addressId != 0 || addressId != '') {
        checkAdd = await addressModel.checkedAddress(addressId, userId, checkPudList, userAgent);
        if (think.isEmpty(checkAdd)) {
          throw this.throwError(-1, '收货地址有误');
        }

        freightPrice = checkAdd.freight_price;
      }else{
        // 查出用户默认地址
        checkAdd = await addressModel.where({
          user_id: userId,
          is_default: 1,
          is_delete: 0
        }).find();
        if (!think.isEmpty(checkAdd)) {
          checkAdd = await addressModel.checkedAddress(checkAdd.id, userId, checkPudList, userAgent);
          if (!think.isEmpty(checkAdd)) {
            freightPrice = checkAdd.freight_price;
          }
        }
      }
      

      return {
        goodsTotalPrice: goodsTotalPrice.toFixed(2),
        freightPrice: freightPrice.toFixed(2),
        checkedGoodsList: checkPudList,
        checkedAddress: checkAdd
      };
    } catch (error) {
      throw error;
    }
  }
};
