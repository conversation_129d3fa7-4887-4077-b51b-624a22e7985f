/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-29 23:48:56
 * @FilePath: /petshop-server/src/api/service/token.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const jwt = require('jsonwebtoken');
const secret = 'sdfsdfsdf123123!ASDasdasdasdasda';
module.exports = class extends think.Service {
    /**
     * 根据header中的x-Auth-token值获取用户id
     */
    getUserId(token) {
        if (!token) {
            return 0;
        }
        const result = this.parse(token);
        if (think.isEmpty(result) || result.user_id <= 0) {
            return 0;
        }
        return result.user_id || result.id;
    }
    getUserType(token) {
        if (!token) {
            return "";
        }
        const result = this.parse(token);
        if (think.isEmpty(result) || !result.userType) {
            return "";
        }
        return result.userType;
    }
    parse(token) {
        if (token) {
            try {
                return jwt.verify(token, secret);
            } catch (err) {
                // if (err.name === 'TokenExpiredError') {
                //     console.log('Token 已过期');
                // } else {
                //     console.log('无效 Token');
                // }
                return null;
            }
        }
        return null;
    }
    async create(userInfo) {
        const payload = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo;

        const token = jwt.sign(payload, secret, {expiresIn: '30d'});
        return token;
    }
};