const Base = require('../../common/controller/base.js');
const moment = require('moment');
const generate = require('nanoid/generate');
const AlipaySdk = require('alipay-sdk');
const fs = require('fs');
const path = require('path');
const isProd = think.env === 'production';
const Wechatpay = require('wechatpay-node-v3');

const alipaySdk = new AlipaySdk({
    appId: '2021005145694479',
    keyType: 'PKCS8',
    privateKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/private-key.pem'), 'ascii'),
    alipayPublicKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/alipay-public-key.pem'), 'ascii'),
});
const alipay_notify_url = isProd ? 'http://*************:8360/api/pay/aliPayNotify' : 'http://***************:8360/api/pay/aliPayNotify'
const wxpay_notify_url = isProd ? 'http://*************:8360/api/pay/wxPayNotify' : 'http://***************:8360/api/pay/wxPayNotify'

const wechatpay = new Wechatpay({
    mchid: '1715440051',
    serial: '7E20A36909FD55C3776D818355C4F1CB07C27EC8',
    privateKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/weixin-private-key.pem'), 'ascii'),
    publicKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/wexin-apiclient_cert.pem'), 'ascii'),
});

module.exports = class extends Base {

    //todo 查询订单状态的接口改成 查询支付宝和微信接口

    async prePay() {
        const orderId = this.post('orderId');
        const userAgent = this.getUserAgent();
        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        // 再次确认库存和价格
        let orderGoods = await this.model('order_goods').where({
            order_id:orderId,
            is_delete:0
        }).select();
        let checkPrice = 0;
        let checkStock = 0;
        for(const item of orderGoods){
            let product = await this.model('product').where({
                id:item.product_id
            }).find();
            if(item.number > product.goods_number){
                checkStock++;
            }
            const realPrice = userAgent ? product.agent_price : product.retail_price;
            if(item.retail_price != realPrice){
                checkPrice++;
            }
        }
        if(checkStock > 0){
            return '库存不足，请重新下单';
        }
        if(checkPrice > 0){
            return '价格发生变化，请重新下单';
        }
        if (think.isEmpty(orderInfo)) {
            return '订单已取消';
        }
        if (parseInt(orderInfo.order_status) != 101) {
            return "订单状态有误";
        }
        if (orderInfo.offline_pay == 1) {
            return "该订单为线下付款，不能使用在线支付";
        }

        return "";
    }

    async weixinPayAction() {
        const order_sn = this.post('orderSn');
        const orderId = this.post('orderId');
        if (!orderId || !order_sn) {
            return this.fail(-1, '参数错误');
        }
        const checkPre = await this.prePay();
        if (checkPre) {
            return this.fail(400, checkPre);
        }

        // order_price 查出该订单的实际需要支付金额
        let orderWhere = {}
        if (orderId) {
            orderWhere.id = orderId;
        } 
        if (order_sn) {
            orderWhere.order_sn = order_sn;
        }
        const orderInfo = await this.model('order').where(orderWhere).find();
        if (think.isEmpty(orderInfo)) {
            return this.fail(-1, '订单不存在');
        }
        const order_price = orderInfo.actual_price || 0;

        if (order_price <= 0) {
            return this.fail(-1, '订单金额有误');
        }

        const result = await wechatpay.transactions_app({
            appid: 'wxf54e017f3b833a82',
            description: '唯优众宠订单',
            out_trade_no: order_sn,
            amount: {
                total: parseFloat(order_price) * 100
            },
            notify_url: wxpay_notify_url
        });
        think.logger.info('微信支付下单结果：', result);
        if (result.status != 200) {
            return this.fail(400, result.error);
        }
        return this.success(result.data);
    }

    async wxPayNotifyAction() {
        const resource = this.post('resource');
        
        think.logger.info(`原始微信回调resource，: ${JSON.stringify(resource)}`);
        const result = wechatpay.decipher_gcm(
            resource.ciphertext, resource.associated_data, resource.nonce, think.config('weixin.partner_key'));
        
        think.logger.info(`解密微信回调result，: ${JSON.stringify(result)}`);
        
        if (!result) {
            think.logger.error('微信支付回调解密失败');
            return this.json({code:400,message:'微信支付回调解密失败'});
        }
        
        if (result.trade_state === 'SUCCESS') {
            // 判断是否为充值订单
            if (result.out_trade_no.startsWith('RC')) {
                const recharge = await this.afterRechargePayment(result);
                if (!recharge) {
                    think.logger.error('微信充值回调处理失败');
                    return this.json({code:400,message:'微信充值回调处理失败'});
                }
            } else {
                const order = await this.afterWxPay(result);
                if (!order) {
                    think.logger.error('微信订单回调处理失败');
                    return this.json({code:400,message:'微信订单回调处理失败'});
                }
            }
            return this.json({code:200,message:'success'});
        }
        return this.json({code:200,message:'success'});
    }

    async wxPayRefundNotifyAction() {
        const resource = this.post('resource');

        think.logger.info(`原始微信退款回调resource，: ${JSON.stringify(resource)}`);
        const result = wechatpay.decipher_gcm(
            resource.ciphertext, resource.associated_data, resource.nonce, think.config('weixin.partner_key'));

        think.logger.info(`解密微信退款回调result，: ${JSON.stringify(result)}`);

        if (!result) {
            think.logger.error('微信退款回调解密失败');
            return this.json({code:400,message:'微信退款回调解密失败'});
        }
        if (result.refund_status === 'SUCCESS') {
            // 判断是否为充值订单
            if (result.out_refund_no.startsWith('RC')) {
                //todo 处理充值退款，如果需要的话
                think.logger.info('微信充值退款回调，暂未实现处理逻辑');
            } else {
                const refund = await this.afterWxPayRefund(result);
                if (!refund) {
                    think.logger.error('微信退款回调处理失败');
                    return this.json({code:400,message:'微信退款回调处理失败'});
                }
            }
            return this.json({code:200,message:'success'});
        }
        return this.json({code:400,message:''});
    }

    async afterWxPay(orderInfo) {
        try {
            await this.transaction("order", async session => {
                const orderModel = this.model('order').db(session);
                const orderGoodsModel = this.model('order_goods').db(session);
                const goodsModel = this.model('goods').db(session);
                const productModel = this.model('product').db(session);

                // 记录订单号
                const outTradeNo = orderInfo.out_trade_no;
                think.logger.info(`处理微信回调，订单号: ${outTradeNo}`);

                // 1. 查找订单
                const order = await orderModel.where({
                    order_sn: outTradeNo,
                    is_delete: 0
                }).find();

                if (think.isEmpty(order)) {
                    think.logger.error(`找不到订单: ${outTradeNo}`);
                    return true;
                }

                // 2. 幂等判断：如果已支付，不重复处理
                if (order.order_status >= 201) {
                    think.logger.info(`订单 ${outTradeNo} 已经处理过支付，当前状态: ${order.order_status}`);
                    return true;
                }

                // 3. 获取订单商品明细（提前获取，减少事务内查询）
                const orderGoodsList = await orderGoodsModel.where({ order_id: order.id }).select();

                // 4. 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];

                // 更新订单状态为已支付
                updatePromises.push(
                    orderModel.where({ id: order.id }).update({
                        order_status: 201, // 已付款
                        pay_type: 'wx',
                        pay_time: Math.floor(Date.now() / 1000),
                        pay_id: orderInfo.transaction_id,
                        actual_price: orderInfo.amount.total / 100
                    })
                );
                
                // 添加交易记录
                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        user_id: order.user_id,
                        log_type: 'CS', // CS: 消费
                        pay_status: 201, // 已付款
                        pay_type: 'wx',
                        amount: orderInfo.amount.total / 100,
                        pay_id: orderInfo.transaction_id,
                        request_data: JSON.stringify(orderInfo),
                        response_data: JSON.stringify({
                            success: true,
                            message: '微信支付成功'
                        })
                    })
                );

                // 并发执行订单状态更新和交易记录
                await Promise.all(updatePromises);
                
                think.logger.info(`订单 ${outTradeNo} 状态已更新为已支付`);

                // 5. 批量更新库存和销量（并发执行）
                const stockUpdatePromises = [];
                for (const item of orderGoodsList) {
                    const { goods_id, product_id, number } = item;

                    // 减商品总库存
                    stockUpdatePromises.push(
                        goodsModel.where({ id: goods_id }).decrement('goods_number', number)
                    );

                    // 加销量
                    stockUpdatePromises.push(
                        goodsModel.where({ id: goods_id }).increment('sell_volume', number)
                    );

                    // 减具体 SKU 库存
                    stockUpdatePromises.push(
                        productModel.where({ id: product_id }).decrement('goods_number', number)
                    );
                }
                
                // 并发执行库存更新
                await Promise.all(stockUpdatePromises);
                
                think.logger.info(`订单 ${orderInfo.out_trade_no} 支付完成，微信交易信息：`, orderInfo);
                think.logger.info(`订单 ${outTradeNo} 商品库存和销量已更新`);
                return true;
            });

            return true;
        } catch (error) {
            think.logger.error('处理微信支付回调时出错:', error);
            return false; // 返回 false 而不是抛出异常
        }
    }

    async afterWxPayRefund(orderInfo) {
        try {
            await this.transaction('order', async (session) => { 
                // 查出订单最新信息
                const order = await this.model('order').db(session).where({
                    order_sn: orderInfo.out_trade_no
                }).find();

                if (think.isEmpty(order)) {
                    think.logger.error(`找不到订单: ${orderInfo.out_trade_no}`);
                    return true;
                }

                // 设置订单退款中状态
                let updateInfo = {
                    order_status: parseFloat(order.actual_price) > parseFloat(orderInfo.refund_fee) ? 206 : 203,
                    refund_time: parseInt(new Date().getTime() / 1000),
                    refund_id: orderInfo.transaction_id
                };
                
                // 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];
                
                // 发起成功之后将订单状态修改为已退款
                updatePromises.push(
                    this.model('order').db(session).where({
                        order_sn: orderInfo.out_trade_no
                    }).update(updateInfo)
                );
                
                // 添加交易记录
                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        user_id: order.user_id,
                        log_type: 'RF', // RF: 退款
                        pay_status: updateInfo.order_status,
                        pay_type: 'wx',
                        amount: orderInfo.refund_fee / 100,
                        pay_id: orderInfo.transaction_id,
                        refund_id: orderInfo.refund_id,
                        request_data: JSON.stringify(orderInfo),
                        response_data: JSON.stringify({
                            success: true,
                            message: '微信退款成功'
                        })
                    })
                );
                
                // 并发执行订单状态更新和交易记录
                await Promise.all(updatePromises);
                
                // 如果是余额支付的退款，还需要处理余额
                if (order.pay_type === 'balance') {
                    // 查询用户当前余额
                    const user = await this.model('user').db(session).where({
                        id: order.user_id
                    }).find();
                    
                    const currentBalance = parseFloat(user.balance || 0);
                    const refundAmount = parseFloat(orderInfo.refund_fee) / 100;
                    const newBalance = currentBalance + refundAmount;
                    
                    // 批量处理余额相关操作
                    const balanceUpdatePromises = [];
                    
                    // 更新用户余额
                    balanceUpdatePromises.push(
                        this.model('user').db(session).where({
                            id: order.user_id
                        }).update({
                            balance: newBalance
                        })
                    );
                    
                    // 添加余额变动记录
                    balanceUpdatePromises.push(
                        this.model('balance_log').db(session).add({
                            user_id: order.user_id,
                            amount: refundAmount,
                            balance: newBalance,
                            log_type: 'RF', // RF: 退款
                            order_sn: order.order_sn,
                            remark: `订单退款：${order.order_sn}，退款金额：${refundAmount}元`
                        })
                    );
                    
                    // 并发执行余额更新
                    await Promise.all(balanceUpdatePromises);
                }

                return true;
            });

            return true;
        } catch (error) {
            think.logger.error('处理微信退款回调时出错:', error);
            return false; // 返回 false 而不是抛出异常
        }
    }

    /**
     * 支付宝支付
     */
    async aliPayAction() {
        const order_sn = this.post('orderSn');
        const orderId = this.post('orderId');
        if (!orderId || !order_sn) {
            return this.fail(-1, '参数错误');
        }
        const checkPre = await this.prePay();
        if (checkPre) {
            return this.fail(400, checkPre);
        }

        // order_price 查出该订单的实际需要支付金额
        let orderWhere = {}
        if (orderId) {
            orderWhere.id = orderId;
        } 
        if (order_sn) {
            orderWhere.order_sn = order_sn;
        }
        const orderInfo = await this.model('order').where(orderWhere).find();
        if (think.isEmpty(orderInfo)) {
            return this.fail(-1, '订单不存在');
        }
        const order_price = orderInfo.actual_price || 0;

        if (order_price <= 0) {
            return this.fail(-1, '订单金额有误');
        }

        const result = await alipaySdk.sdkExec('alipay.trade.app.pay', {
            notify_url: alipay_notify_url, // 通知回调地址
            bizContent: {
                out_trade_no: order_sn,
                total_amount: order_price,
                subject: '唯优众宠订单',
            }
        });
        think.logger.info('支付宝支付', result);
        if (think.isEmpty(result)) {
            return this.fail('支付失败');
        }
        return this.success(result);
    }
    async aliPayNotifyAction() {
        // 获取原始请求数据，支付宝回调使用 application/x-www-form-urlencoded 格式
        const orderInfo = this.ctx.request.body;
        try {
            // 验证签名
            const signVerified = await alipaySdk.checkNotifySign(orderInfo);

            if (!signVerified) {
                think.logger.error('支付宝回调签名验证失败');
                // 按照支付宝的要求，需要返回 "success" 字符串
                this.ctx.type = 'text/plain';
                this.ctx.body = 'success';
                return;
            }

            // 处理交易成功的情况
            if (orderInfo.trade_status === 'TRADE_SUCCESS') {
                // 判断是否为充值订单
                if (orderInfo.out_trade_no.startsWith('RC')) {
                    const recharge = await this.afterRechargePayment(orderInfo);
                    if (!recharge) {
                        think.logger.error('支付宝充值回调处理失败，但返回success避免重复回调');
                        // 按照支付宝的要求，需要返回 "success" 字符串
                        this.ctx.type = 'text/plain';
                        this.ctx.body = 'success';
                        return;
                    }
                } else {
                    const order = await this.afterAliPay(orderInfo);
                    if (!order) {
                        think.logger.error('支付宝订单回调处理失败，但返回success避免重复回调');
                        // 按照支付宝的要求，需要返回 "success" 字符串
                        this.ctx.type = 'text/plain';
                        this.ctx.body = 'success';
                        return;
                    }
                }
            } else if (orderInfo.trade_status === 'TRADE_CLOSED') {
                if (orderInfo.out_trade_no.startsWith('RC')) {
                    //todo 处理充值退款，如果需要的话
                } else {
                    const refund = await this.aliPayRefund(orderInfo);
                    if (!refund) {
                        think.logger.error('支付宝退款回调处理失败，但返回success避免重复回调');
                        // 按照支付宝的要求，需要返回 "success" 字符串
                        this.ctx.type = 'text/plain';
                        this.ctx.body = 'success';
                        return;
                    }
                }
            }

            // 按照支付宝的要求，需要返回 "success" 字符串
            this.ctx.type = 'text/plain';
            this.ctx.body = 'success';
            return;
        } catch (error) {
            think.logger.error('处理支付宝回调时发生错误', JSON.stringify(orderInfo), error);
            // 即使发生异常，也要按照支付宝的要求返回 "success" 字符串
            // 避免支付宝重复发送回调
            this.ctx.type = 'text/plain';
            this.ctx.body = 'success';
            return;
        }
    }

    async afterAliPay(orderInfo) {
        try {
            await this.transaction("order", async session => {
                const orderModel = this.model('order').db(session);
                const orderGoodsModel = this.model('order_goods').db(session);
                const goodsModel = this.model('goods').db(session);
                const productModel = this.model('product').db(session);

                // 记录订单号
                const outTradeNo = orderInfo.out_trade_no;
                think.logger.info(`处理支付宝回调，订单号: ${outTradeNo}`);

                // 1. 查找订单
                const order = await orderModel.where({
                    order_sn: outTradeNo,
                    is_delete: 0
                }).find();

                if (think.isEmpty(order)) {
                    think.logger.error(`找不到订单: ${outTradeNo}`);
                    return true;
                }

                // 2. 幂等判断：如果已支付，不重复处理
                if (order.order_status >= 201) {
                    think.logger.info(`订单 ${outTradeNo} 已经处理过支付，当前状态: ${order.order_status}`);
                    return true;
                }

                // 3. 获取订单商品明细（提前获取，减少事务内查询）
                const orderGoodsList = await orderGoodsModel.where({ order_id: order.id }).select();

                // 4. 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];

                // 更新订单状态为已支付
                updatePromises.push(
                    orderModel.where({ id: order.id }).update({
                        order_status: 201, // 已付款
                        pay_type: 'ali',
                        pay_time: Math.floor(Date.now() / 1000),
                        pay_id: orderInfo.trade_no,
                        actual_price: orderInfo.total_amount
                    })
                );
                
                // 添加交易记录
                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        user_id: order.user_id,
                        log_type: 'CS', // CS: 消费
                        pay_status: 201, // 已付款
                        pay_type: 'ali',
                        amount: orderInfo.total_amount,
                        pay_id: orderInfo.trade_no,
                        request_data: JSON.stringify(orderInfo),
                        response_data: JSON.stringify({
                            success: true,
                            message: '支付宝支付成功'
                        })
                    })
                );

                // 并发执行订单状态更新和交易记录
                await Promise.all(updatePromises);
                
                think.logger.info(`订单 ${outTradeNo} 状态已更新为已支付`);

                // 5. 批量更新库存和销量（并发执行）
                const stockUpdatePromises = [];
                for (const item of orderGoodsList) {
                    const { goods_id, product_id, number } = item;

                    // 减商品总库存
                    stockUpdatePromises.push(
                        goodsModel.where({ id: goods_id }).decrement('goods_number', number)
                    );

                    // 加销量
                    stockUpdatePromises.push(
                        goodsModel.where({ id: goods_id }).increment('sell_volume', number)
                    );

                    // 减具体 SKU 库存
                    stockUpdatePromises.push(
                        productModel.where({ id: product_id }).decrement('goods_number', number)
                    );
                }
                
                // 并发执行库存更新
                await Promise.all(stockUpdatePromises);
                
                think.logger.info(`订单 ${orderInfo.out_trade_no} 支付完成，支付宝交易信息：`, orderInfo);
                think.logger.info(`订单 ${outTradeNo} 商品库存和销量已更新`);
                return true;
            });
            return true; // 成功处理
        } catch (error) {
            think.logger.error('处理支付宝支付回调时出错:', error);
            return false; // 返回 false 而不是抛出异常
        }
    }

    async aliPayRefund(orderInfo) {
        try {
            await this.transaction('order', async (session) => { 
                // 查出订单最新信息
                const order = await this.model('order').db(session).where({
                    order_sn: orderInfo.out_trade_no
                }).find();

                if (think.isEmpty(order)) {
                    think.logger.error(`找不到订单: ${orderInfo.out_trade_no}`);
                    return true;
                }

                // 设置订单退款中状态
                let updateInfo = {
                    order_status: parseFloat(order.actual_price) > parseFloat(orderInfo.refund_fee) ? 206 : 203,
                    refund_time: parseInt(new Date().getTime() / 1000),
                    refund_id: orderInfo.trade_no
                };
                
                // 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];
                
                // 发起成功之后将订单状态修改为已退款
                updatePromises.push(
                    this.model('order').db(session).where({
                        order_sn: orderInfo.out_trade_no
                    }).update(updateInfo)
                );
                
                // 添加交易记录
                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        user_id: order.user_id,
                        log_type: 'RF', // RF: 退款
                        pay_status: updateInfo.order_status,
                        pay_type: 'ali',
                        amount: orderInfo.refund_fee,
                        pay_id: orderInfo.trade_no,
                        refund_id: orderInfo.refund_id,
                        request_data: JSON.stringify(orderInfo),
                        response_data: JSON.stringify({
                            success: true,
                            message: '支付宝退款成功'
                        })
                    })
                );
                
                // 并发执行订单状态更新和交易记录
                await Promise.all(updatePromises);
                
                // 如果是余额支付的退款，还需要处理余额
                if (order.pay_type === 'balance') {
                    // 查询用户当前余额
                    const user = await this.model('user').db(session).where({
                        id: order.user_id
                    }).find();
                    
                    const currentBalance = parseFloat(user.balance || 0);
                    const refundAmount = parseFloat(orderInfo.refund_fee);
                    const newBalance = currentBalance + refundAmount;
                    
                    // 批量处理余额相关操作
                    const balanceUpdatePromises = [];
                    
                    // 更新用户余额
                    balanceUpdatePromises.push(
                        this.model('user').db(session).where({
                            id: order.user_id
                        }).update({
                            balance: newBalance
                        })
                    );
                    
                    // 添加余额变动记录
                    balanceUpdatePromises.push(
                        this.model('balance_log').db(session).add({
                            user_id: order.user_id,
                            amount: refundAmount,
                            balance: newBalance,
                            log_type: 'RF', // RF: 退款
                            order_sn: order.order_sn,
                            remark: `订单退款：${order.order_sn}，退款金额：${refundAmount}元`
                        })
                    );
                    
                    // 并发执行余额更新
                    await Promise.all(balanceUpdatePromises);
                }
            });
            return true; // 成功处理
        } catch (error) {
            think.logger.error('处理支付宝退款回调时出错:', error);
            return false; // 返回 false 而不是抛出异常
        }
    }

    /**
     * 支付宝充值支付
     */
    async aliPayRechargeAction() {
        const rechargeNo = this.get('rechargeNo');
        const amount = this.get('amount');
        
        if (!rechargeNo || !amount) {
            return this.fail(-1, '参数错误');
        }
        
        const result = await alipaySdk.sdkExec('alipay.trade.app.pay', {
            notify_url: alipay_notify_url, // 通知回调地址
            bizContent: {
                out_trade_no: rechargeNo,
                total_amount: amount,
                subject: '唯优众宠充值',
            }
        });
        
        think.logger.info('支付宝充值', result);
        if (think.isEmpty(result)) {
            return this.fail('支付失败');
        }
        return this.success({
            aliPayment: result,
            rechargeNo: rechargeNo,
            amount: amount
        });
    }

    /**
     * 微信充值支付
     */
    async weixinPayRechargeAction() {
        const rechargeNo = this.get('rechargeNo');
        const amount = this.get('amount');
        
        if (!rechargeNo || !amount) {
            return this.fail(-1, '参数错误');
        }
        
        const result = await wechatpay.transactions_app({
            appid: 'wxf54e017f3b833a82',
            description: '唯优众宠充值',
            out_trade_no: rechargeNo,
            amount: {
                total: parseFloat(amount) * 100
            },
            notify_url: wxpay_notify_url
        });
        
        think.logger.info('微信充值下单结果：', result);
        if (result.status != 200) {
            return this.fail(400, result.error);
        }
        return this.success({
            wxPayment: result.data,
            rechargeNo: rechargeNo,
            amount: amount
        });
    }

    /**
     * 处理充值支付回调
     */
    async afterRechargePayment(paymentInfo) {
        const maxRetries = 3;
        let retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                // 先检查是否已经处理过（幂等性检查）
                const existingRecharge = await this.model('recharge').where({
                    recharge_no: paymentInfo.out_trade_no,
                    pay_status: 201 // 已付款状态
                }).find();
                
                if (!think.isEmpty(existingRecharge)) {
                    think.logger.info(`充值订单 ${paymentInfo.out_trade_no} 已经处理过`);
                    return true;
                }
                
                await this.transaction('recharge', async (session) => {
                    // 查询充值记录和用户信息（一次性查询，减少数据库交互）
                    const recharge = await this.model('recharge').db(session).where({
                        recharge_no: paymentInfo.out_trade_no,
                        pay_status: 101 // 未付款状态
                    }).find();
                    
                    if (think.isEmpty(recharge)) {
                        think.logger.info(`充值订单 ${paymentInfo.out_trade_no} 不存在或已处理`);
                        return true;
                    }
                    
                    // 查询用户信息
                    const user = await this.model('user').db(session).where({
                        id: recharge.user_id
                    }).find();
                    
                    if (think.isEmpty(user)) {
                        think.logger.error(`用户 ${recharge.user_id} 不存在`);
                        return true;
                    }
                    
                    // 计算赠送金额
                    const rechargeAmount = parseFloat(recharge.amount);
                    let bonusAmount = 0;
                    let remarkBonus = '';
                    
                    // 根据用户类型和充值金额计算赠送金额
                    if (user.is_agent === 1) {
                        // 代理商充值优惠规则
                        if (rechargeAmount === 5000) {
                            bonusAmount = 30;
                            remarkBonus = '门店医院充值5000元赠送30元';
                        } else if (rechargeAmount === 10000) {
                            bonusAmount = 60;
                            remarkBonus = '门店医院充值10000元赠送60元';
                        }
                    } else {
                        // 普通用户充值优惠规则
                        if (rechargeAmount === 500) {
                            bonusAmount = 20;
                            remarkBonus = '会员充值500元赠送20元';
                        } else if (rechargeAmount === 1000) {
                            bonusAmount = 50;
                            remarkBonus = '会员充值1000元赠送50元';
                        } else if (rechargeAmount === 3000) {
                            bonusAmount = 120;
                            remarkBonus = '会员充值3000元赠送120元';
                        } else if (rechargeAmount === 5000) {
                            bonusAmount = 200;
                            remarkBonus = '会员充值5000元赠送200元';
                        } else if (rechargeAmount === 10000) {
                            bonusAmount = 300;
                            remarkBonus = '会员充值10000元赠送300元';
                        }
                    }
                    
                    // 计算最终到账金额
                    const currentBalance = parseFloat(user.balance || 0);
                    const totalAmount = rechargeAmount + bonusAmount;
                    const newBalance = currentBalance + totalAmount;
                    
                    // 批量更新操作（减少数据库交互次数）
                    const updatePromises = [];
                    
                    // 1. 更新充值记录状态
                    updatePromises.push(
                        this.model('recharge').db(session).where({
                            id: recharge.id
                        }).update({
                            pay_status: 201, // 已付款
                            pay_time: parseInt(new Date().getTime() / 1000),
                            pay_id: paymentInfo.transaction_id || paymentInfo.trade_no
                        })
                    );
                    
                    // 2. 更新用户余额
                    updatePromises.push(
                        this.model('user').db(session).where({
                            id: recharge.user_id
                        }).update({
                            balance: newBalance
                        })
                    );
                    
                    // 3. 添加余额变动记录
                    const payTypeText = paymentInfo.transaction_id ? '微信支付' : '支付宝支付';
                    const payType = paymentInfo.transaction_id ? 'wx' : 'ali';
                    const remarkText = `账户充值：${recharge.amount}元，支付方式：${payTypeText}，充值单号：${recharge.recharge_no}，${remarkBonus}`;
                    
                    updatePromises.push(
                        this.model('balance_log').db(session).add({
                            user_id: recharge.user_id,
                            amount: totalAmount,
                            balance: newBalance,
                            log_type: 'RC', // RC: 充值
                            order_sn: recharge.recharge_no,
                            remark: remarkText
                        })
                    );
                    
                    // 4. 添加交易记录
                    updatePromises.push(
                        this.model('transaction_log').db(session).add({
                            order_id: recharge.id, // 使用充值记录ID
                            user_id: recharge.user_id,
                            log_type: 'RC', // RC: 充值
                            pay_status: 201, // 已付款
                            pay_type: payType,
                            amount: recharge.amount,
                            pay_id: paymentInfo.transaction_id || paymentInfo.trade_no,
                            request_data: JSON.stringify(paymentInfo),
                            response_data: JSON.stringify({
                                success: true,
                                message: '充值成功'
                            })
                        })
                    );
                    
                    // 并发执行所有更新操作
                    await Promise.all(updatePromises);

                    think.logger.info(`用户 ${recharge.user_id} 充值 ${recharge.amount} 元成功，赠送 ${bonusAmount} 元，当前余额 ${newBalance} 元`);
                    return true;
                });

                return true;
            } catch (error) {
                retryCount++;
                think.logger.error(`处理充值回调时出错 (第${retryCount}次重试):`, error);
                
                if (retryCount >= maxRetries) {
                    think.logger.error(`充值处理失败，已重试${maxRetries}次，充值单号: ${paymentInfo.out_trade_no}`);
                    return false;
                }
                
                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
        }
        
        return false;
    }

    /**
     * 余额支付
     */
    async balancePayAction() {
        const order_sn = this.post('orderSn');
        const order_price = this.post('orderPrice');
        const orderId = this.post('orderId');
        
        if (!orderId || !order_sn || !order_price) {
            return this.fail(-1, '参数错误');
        }
        
        const checkPre = await this.prePay();
        if (checkPre) {
            return this.fail(400, checkPre);
        }
        
        try {
            await this.transaction("order", async session => {
                const userId = this.getLoginUserId();
                
                // 1. 查询用户余额
                const user = await this.model('user').db(session).where({
                    id: userId
                }).find();
                
                const balance = parseFloat(user.balance || 0);
                const orderAmount = parseFloat(order_price);
                
                // 2. 检查余额是否足够
                if (balance < orderAmount) {
                    throw new Error('余额不足，请充值');
                }
                
                // 3. 查找订单
                const order = await this.model('order').db(session).where({
                    id: orderId,
                    order_sn: order_sn,
                    is_delete: 0
                }).find();
                
                if (think.isEmpty(order)) {
                    throw new Error('订单不存在');
                }
                
                // 4. 幂等判断：如果已支付，不重复处理
                if (order.order_status >= 201) {
                    throw new Error('订单已支付');
                }
                
                // 5. 获取订单商品明细（提前获取，减少事务内查询）
                const orderGoodsList = await this.model('order_goods').db(session).where({ 
                    order_id: order.id 
                }).select();
                
                // 6. 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];
                
                // 扣减用户余额
                const newBalance = balance - orderAmount;
                updatePromises.push(
                    this.model('user').db(session).where({
                        id: userId
                    }).update({
                        balance: newBalance
                    })
                );
                
                // 更新订单状态为已支付
                updatePromises.push(
                    this.model('order').db(session).where({ id: order.id }).update({
                        order_status: 201, // 已付款
                        pay_type: 'balance',
                        pay_time: Math.floor(Date.now() / 1000),
                        actual_price: orderAmount
                    })
                );
                
                // 添加余额变动记录
                updatePromises.push(
                    this.model('balance_log').db(session).add({
                        user_id: userId,
                        amount: orderAmount,
                        balance: newBalance,
                        log_type: 'CS', // CS: 消费
                        order_sn: order_sn,
                        remark: `订单支付：${order_sn}`
                    })
                );
                
                // 添加交易记录
                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        user_id: userId,
                        log_type: 'CS', // CS: 消费
                        pay_status: 201, // 已付款
                        pay_type: 'balance',
                        amount: orderAmount,
                        request_data: JSON.stringify({
                            order_sn: order_sn,
                            order_price: orderAmount
                        }),
                        response_data: JSON.stringify({
                            success: true,
                            message: '余额支付成功'
                        })
                    })
                );
                
                // 并发执行所有更新操作
                await Promise.all(updatePromises);
                
                // 7. 批量更新商品库存和销量（并发执行）
                const stockUpdatePromises = [];
                for (const item of orderGoodsList) {
                    const { goods_id, product_id, number } = item;
                    
                    // 减商品总库存
                    stockUpdatePromises.push(
                        this.model('goods').db(session).where({ id: goods_id }).decrement('goods_number', number)
                    );
                    
                    // 加销量
                    stockUpdatePromises.push(
                        this.model('goods').db(session).where({ id: goods_id }).increment('sell_volume', number)
                    );
                    
                    // 减具体 SKU 库存
                    stockUpdatePromises.push(
                        this.model('product').db(session).where({ id: product_id }).decrement('goods_number', number)
                    );
                }
                
                // 并发执行库存更新
                await Promise.all(stockUpdatePromises);
                
                think.logger.info(`订单 ${order_sn} 余额支付成功，金额: ${orderAmount}，用户余额: ${newBalance}`);
                return true;
            });
            
            return this.success({
                message: '支付成功'
            });
        } catch (error) {
            think.logger.error('余额支付失败:', error);
            return this.fail(400, error.message || '支付失败');
        }
    }

    /**
     * 查询充值结果
     */
    async rechargeStatusAction() {
        const userId = this.getLoginUserId();
        const rechargeNo = this.post('rechargeNo');
        const rechargeId = this.post('rechargeId');
        if (think.isEmpty(rechargeNo) && think.isEmpty(rechargeId)) {
            return this.fail('参数错误');
        }
        let where = {
            user_id: userId
        };
        if (!think.isEmpty(rechargeNo)) {
            where.recharge_no = rechargeNo;
        }
        if (!think.isEmpty(rechargeId)) {
            where.id = rechargeId;
        }
        const recharge = await this.model('recharge').where(where).find();
        return this.success({
            pay_status: recharge.pay_status
        });
    }
};
