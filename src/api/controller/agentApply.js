/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-07 17:34:45
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-08 22:02:00
 * @FilePath: /petshop-server/src/api/controller/agentApply.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
const moment = require('moment');
const _ = require('lodash');
const rp = require('request-promise');
const fs = require('fs');
const http = require("http");
module.exports = class extends Base {
    async getAgentApplyAction() {
        const userId = this.getLoginUserId();
        const model = this.model('agent_apply');
        const agentApply = await model.alias('a')
                        .field('a.*,p.id as province,c.id as city,d.id as district,p.name as province_name,c.name as city_name,d.name as district_name')
                        .join({
                            table: 'region',
                            join: 'left',
                            as: 'p',
                            on: ['p.id', 'a.province']
                        })
                        .join({
                            table: 'region',
                            join: 'left',
                            as: 'c',
                            on: ['c.id', 'a.city']
                        })
                        .join({
                            table: 'region',
                            join: 'left',
                            as: 'd',
                            on: ['d.id', 'a.district']
                        }).where({
                            user_id: userId,
                            is_delete: 0
                        }).find();

        return this.success(agentApply);
    }
    async applyAction() {
        const userId = this.getLoginUserId();
        const model = this.model('agent_apply');
        const agentApply = await model.where({
            user_id: userId,
            is_delete: 0
        }).find();
        //   `review_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 待审核 1 审核通过 2 审核拒绝',
        if (!think.isEmpty(agentApply)) {
            if (agentApply.review_status == 0) {
                return this.fail('正在审核中，请勿重复提交');
            }else if (agentApply.status == 1) {
                return this.fail('审核通过，请勿重复提交');
            }
        }
        const data = this.post();
        if (data.id) {
            await model.where({
                id: data.id
            }).update({
                shop_name: data.shop_name,
                shop_link_name: data.shop_link_name,
                shop_link_phone: data.shop_link_phone,
                country: data.country,
                province: data.province,
                city: data.city,
                district: data.district,
                address: data.address,
                review_status: 0
            });
            return this.success();
        }
        data.user_id = userId;
        await model.add(data);
        return this.success();
    }
};