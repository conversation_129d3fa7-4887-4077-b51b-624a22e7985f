const Base = require('../../common/controller/base.js');
const moment = require('moment');
const _ = require('lodash');
const rp = require('request-promise');
const fs = require('fs');
const http = require("http");
module.exports = class extends Base {
    /**
     * 获取订单列表
     * @return {Promise} []
     */
    async listAction() {
        const showType = this.get('showType');
        const userId = this.getLoginUserId();
        const page = this.get('page');
        const size = this.get('size');
        const currentTime = parseInt(Date.now() / 1000);

        let where = {
            'o.user_id': userId,
            'o.is_delete': 0,
            'o.order_type': ['<', 7],
        }
        const statusList = await this.model('order').getOrderStatus(showType);
        if (statusList.length > 0) {
            where['o.order_status'] = ['IN', statusList];
        }
        // 查出订单列表
        const orderList = await this.model('order')
            .alias('o')
            .field('o.*, ct.name as coupon_name')
            .join({
                table: 'coupon_template',
                join: 'left',
                as: 'ct',
                on: ['ct.id', 'o.coupon_template_id']
            })
            .where(where)
            .page(page, size)
            .order('create_time DESC')
            .countSelect();

        const orders = orderList.data;

        if (orders.length === 0) {
            return this.success({ data: [], count: 0 });
        }

        const orderIds = orders.map(o => o.id);

        // 查商品一次性查出所有
        const goodsList = await this.model('order_goods')
            .field('order_id,id,product_id,goods_id,list_pic_url,number,goods_name,goods_specifition_name,retail_price,create_time')
            .where({
                user_id: userId,
                order_id: ['IN', orderIds],
                is_delete: 0
            })
            .select();

        const goodsMap = _.groupBy(goodsList, 'order_id');

        // 要更新状态的订单
        const expiredOrderIds = [];

        for (const order of orders) {
            const isPending = [101, 801].includes(order.order_status);
            const expiredTime = moment(order.create_time).unix() + 24 * 60 * 60;

            if (isPending && expiredTime < currentTime) {
                order.order_status = 102; // 设置为已取消
                expiredOrderIds.push(order.id);
            }

            // 商品信息
            const orderGoods = goodsMap[order.id] || [];
            order.goodsList = orderGoods;
            order.goodsCount = orderGoods.reduce((sum, item) => sum + item.number, 0);
        }

        // 批量更新已过期订单
        if (expiredOrderIds.length > 0) {
            await this.model('order').where({ id: ['IN', expiredOrderIds] }).update({ order_status: 102 });
        }

        // 并发获取订单状态文本和操作按钮（如内部无多余查询可保留）
        await Promise.all(
                orders.map(async order => {
                order.order_status_text = await this.model('order').getOrderStatusText(order.order_status);
                order.handleOption = await this.model('order').getOrderHandleOption(order.id, order);
            })
        );

        orderList.data = orders;
        return this.success(orderList);
    }
    // 获得订单数量
    //
    async countAction() {
        const showType = this.get('showType');
		const userId = this.getLoginUserId();;
        let status = [];
        status = await this.model('order').getOrderStatus(showType);
        let is_delete = 0;
        const allCount = await this.model('order').where({
            user_id: userId,
            is_delete: is_delete,
            order_status: ['IN', status]
        }).count('id');
        return this.success({
            allCount: allCount,
        });
    }
    // 获得订单数量状态
    //
    async orderCountAction() {
		const user_id = this.getLoginUserId();
        if(user_id != 0){
            let toPay = await this.model('order').where({
                user_id: user_id,
                is_delete: 0,
                order_type: ['<', 7],
                order_status: ['IN', '101,801']
            }).count('id');
            let toDelivery = await this.model('order').where({
                user_id: user_id,
                is_delete: 0,
                order_type: ['<', 7],
                order_status: ['IN', '201,300']
            }).count('id');
            let toReceive = await this.model('order').where({
                user_id: user_id,
                order_type: ['<', 7],
                is_delete: 0,
                order_status: 301
            }).count('id');
            let afterSales = await this.model('order').where({
                user_id: user_id,
                order_type: ['<', 7],
                is_delete: 0,
                order_status: ['IN', '202,205']
            }).count('id');
            let newStatus = {
                toPay: toPay,
                toDelivery: toDelivery,
                toReceive: toReceive,
                afterSales: afterSales
            }
            return this.success(newStatus);
        }
       
    }
    async detailAction() {
        const orderId = this.get('orderId');
        const orderInfo = await this.model('order')
            .alias('o')
            .field('o.*, ct.name as coupon_name')
            .join({
                table: 'coupon_template',
                join: 'left',
                as: 'ct',
                on: ['ct.id', 'o.coupon_template_id']
            }).where({ 'o.id': orderId }).find();
        const currentTime = parseInt(new Date().getTime() / 1000);
        if (think.isEmpty(orderInfo)) {
            return this.fail('订单不存在');
        }
        orderInfo.province_name = await this.model('region').where({
            id: orderInfo.province
        }).getField('name', true);
        orderInfo.city_name = await this.model('region').where({
            id: orderInfo.city
        }).getField('name', true);
        orderInfo.district_name = await this.model('region').where({
            id: orderInfo.district
        }).getField('name', true);
        orderInfo.full_region = orderInfo.province_name + orderInfo.city_name + orderInfo.district_name;
        const orderGoods = await this.model('order_goods').where({
            order_id: orderId,
            is_delete: 0
        }).select();
        var goodsCount = 0;
        for (const gitem of orderGoods) {
            goodsCount += gitem.number;
        }
        // 订单状态的处理
        orderInfo.order_status_text = await this.model('order').getOrderStatusText(orderInfo.order_status);
        if (think.isEmpty(orderInfo.confirm_time)) {
            orderInfo.confirm_time = 0;
        } else orderInfo.confirm_time = moment.unix(orderInfo.confirm_time).format('YYYY-MM-DD HH:mm:ss');
        if (think.isEmpty(orderInfo.dealdone_time)) {
            orderInfo.dealdone_time = 0;
        } else orderInfo.dealdone_time = moment.unix(orderInfo.dealdone_time).format('YYYY-MM-DD HH:mm:ss');
        if (think.isEmpty(orderInfo.pay_time)) {
            orderInfo.pay_time = 0;
        } else orderInfo.pay_time = moment.unix(orderInfo.pay_time).format('YYYY-MM-DD HH:mm:ss');
        if (think.isEmpty(orderInfo.shipping_time)) {
            orderInfo.shipping_time = 0;
        } else {
            orderInfo.confirm_remainTime = orderInfo.shipping_time + 10 * 24 * 60 * 60;
            orderInfo.shipping_time = moment.unix(orderInfo.shipping_time).format('YYYY-MM-DD HH:mm:ss');
        }
        // 订单支付倒计时
        if (orderInfo.order_status === 101 || orderInfo.order_status === 801) {
            orderInfo.final_pay_time = moment(orderInfo.create_time).valueOf() / 1000 + 24 * 60 * 60; //支付倒计时24小时
            if (orderInfo.final_pay_time < currentTime) {
                //超过时间不支付，更新订单状态为取消
                let updateInfo = {
                    order_status: 102
                };
                await this.model('order').where({
                    id: orderId
                }).update(updateInfo);
            }
        }
        // 订单可操作的选择,删除，支付，收货，评论，退换货
        const handleOption = await this.model('order').getOrderHandleOption(orderId, orderInfo);
        return this.success({
            orderInfo: orderInfo,
            orderGoods: orderGoods,
            handleOption: handleOption,
            goodsCount: goodsCount,
        });
    }
    /**
     * 查询订单状态
     */
    async orderStatusAction() {
        const userId = this.getLoginUserId();
        const orderId = this.post('orderId');
        const orderSn = this.post('orderSn');
        if (think.isEmpty(orderId) && think.isEmpty(orderSn)) {
            return this.fail('订单号不能为空');
        }
        // 使用 orderId 或者 orderSn 查询订单
        let where = {
            user_id: userId,
        };
        if (!think.isEmpty(orderId)) {
            where.id = orderId;
        }
        if (!think.isEmpty(orderSn)) {
            where.order_sn = orderSn;
        }
        const orderInfo = await this.model('order')
                      .where(where).find();
        return this.success({
            orderStatus: orderInfo.order_status
        });
    }
    /**
     * order 和 order-check 的goodslist
     * @return {Promise} []
     */
    async orderGoodsAction() {
		const userId = this.getLoginUserId();;
        const orderId = this.get('orderId');
        if (orderId > 0) {
            const orderGoods = await this.model('order_goods').where({
                user_id: userId,
                order_id: orderId,
                is_delete: 0
            }).select();
            var goodsCount = 0;
            for (const gitem of orderGoods) {
                goodsCount += gitem.number;
            }
            return this.success(orderGoods);
        } else {
            const cartList = await this.model('cart').where({
                user_id: userId,
                checked:1,
                is_delete: 0,
                is_fast: 0,
            }).select();
            return this.success(cartList);
        }
    }
    /**
     * 取消订单
     * @return {Promise} []
     */
    async cancelAction() {
        const orderId = this.post('orderId');
		const userId = this.getLoginUserId();
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderId);
        if (!handleOption.cancel) {
            return this.fail('订单不能取消');
        }
        // 设置订单已取消状态
        let updateInfo = {
            order_status: 102,
            cancel_time: parseInt(new Date().getTime() / 1000),
        };
        
        //取消订单，还原库存
        const goodsInfo = await this.model('order_goods').where({
            order_id: orderId,
            user_id: userId
        }).select();
        for (const item of goodsInfo) {
            let goods_id = item.goods_id;
            let product_id = item.product_id;
            let number = item.number;
            await this.model('goods').where({
                id: goods_id
            }).increment('goods_number', number);
            await this.model('product').where({
                id: product_id
            }).increment('goods_number', number);
        }
        const succesInfo = await this.model('order').where({
            id: orderId
        }).update(updateInfo);
        return this.success(succesInfo);
    }
    /**
     * 订单申请退款
     * @return {Promise} []
     */
    async applyRefundAction() {
        const orderId = this.post('orderId');
        // 检测是否能够取消
        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        const handleOption = await this.model('order').getOrderHandleOption(orderId, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能申请退款');
        }
        const succesInfo = await this.model('order').where({
            id: orderId
        }).update({
            order_status: 202,
            apply_status: orderInfo.order_status
        });
        return this.success(succesInfo);
    }
    /**
     * 取消申请退款
     */
    async refundCancelAction() {
        const orderId = this.post('orderId');
        // 检测是否能够取消
        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        const handleOption = await this.model('order').getOrderHandleOption(orderId, orderInfo);
        if (!handleOption.refund_cancel) {
            return this.fail('订单不能取消退款');
        }
        const succesInfo = await this.model('order').where({
            id: orderId
        }).update({
            order_status: orderInfo.apply_status || 401,
        });
        return this.success(succesInfo);
    }
    
    async addRefundOrderExpressAction() {
        const orderId = this.post('orderId');
        const logistic_code = this.post('logisticCode');
        const sender_phone = this.post('senderPhone');
        const sender_name = this.post('senderName');

        if (think.isEmpty(logistic_code) || think.isEmpty(orderId) 
            || think.isEmpty(sender_phone) || think.isEmpty(sender_name)) {
            return this.fail('参数错误');
        }

        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        if (think.isEmpty(orderInfo)) {
            return this.fail('订单信息错误');
        }
        if (!(orderInfo.order_status == 205)) {
            return this.fail('订单状态错误');
        }
        
        // 调用快递100的接口，识别物流号信息
        const identifyExpressNum = await this.model('order_express').identifyExpressNum(logistic_code);
        think.logger.info("识别快递单号快递100响应：", identifyExpressNum);

        if (think.isEmpty(identifyExpressNum) || !(identifyExpressNum instanceof Array)) {
            return this.fail(identifyExpressNum.message || '无效快递单号');
        }
        const expressInfo = await this.model('order_express').where({
            order_id: orderId,
            express_type: 2,
            is_delete: 0
        }).find();
        if (!think.isEmpty(expressInfo)) {
            await this.model('order_express').where({
                order_id: orderId
            }).update({
                logistic_code: logistic_code,
                shipper_name: identifyExpressNum[0].name,
                shipper_code: identifyExpressNum[0].comCode,
                sender_phone: sender_phone,
                traces: '',
                sender_name: sender_name
            });
        } else {
            await this.model('order_express').add({
                logistic_code: logistic_code,
                express_type: 2,
                order_id: orderId,
                shipper_name: identifyExpressNum[0].name,
                shipper_code: identifyExpressNum[0].comCode,
                sender_phone: sender_phone,
                traces: '',
                sender_name: sender_name
            });
        }
        return this.success();
    }
    /**
     * 删除订单
     * @return {Promise} []
     */
    async deleteAction() {
        const orderId = this.post('orderId');
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderId);
        if (!handleOption.delete) {
            return this.fail('订单不能删除');
        }
        const succesInfo = await this.model('order').orderDeleteById(orderId);
        return this.success(succesInfo);
    }
    /**
     * 确认订单
     * @return {Promise} []
     */
    async confirmAction() {
        const orderId = this.post('orderId');
        const actType = this.post('actType');
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderId);
        if (!handleOption.confirm) {
            return this.fail('订单不能确认');
        }
        if (actType == 'auto') { // 自动确认收货
            // 检查确认订单的时间是否已经到了
            const orderInfo = await this.model('order').where({
                id: orderId
            }).find();
            // 状态只有 已发货，已收货才能自动确认
            if ([301, 302, 303].indexOf(parseInt(orderInfo.order_status)) == -1) {
                return this.fail('状态只有 已发货，已收货才能自动确认');
            }
            // 发货时间不足7天
            if (parseInt(new Date().getTime() / 1000) <orderInfo.shipping_time + 7 * 24 * 60 * 60) {
                return this.fail('发货时间不足7天');
            }
        }
        // 设置订单已取消状态
        const currentTime = parseInt(new Date().getTime() / 1000);
        let updateInfo = {
            order_status: 401,
            confirm_time: currentTime
        };
        const succesInfo = await this.model('order').where({
            id: orderId
        }).update(updateInfo);
        return this.success(succesInfo);
    }
    /**
     * 完成评论后的订单
     * @return {Promise} []
     */
    async completeAction() {
        const orderId = this.post('orderId');
        // 设置订单已完成
        const currentTime = parseInt(new Date().getTime() / 1000);
        let updateInfo = {
            order_status: 401,
            dealdone_time: currentTime
        };
        const succesInfo = await this.model('order').where({
            id: orderId
        }).update(updateInfo);
        return this.success(succesInfo);
    }
    /**
     * 提交订单
     * !!! 已作废
     * @returns {Promise.<void>}
     */
    async submitAction() {
        // 获取收货地址信息和计算运费
		    const userId = this.getLoginUserId();
        const addressId = this.post('addressId');
        const freightPrice = this.post('freightPrice');
        const offlinePay = this.post('offlinePay');
        let postscript = this.post('postscript');
        const checkedAddress = await this.model('address').where({
            id: addressId
        }).find();
        if (think.isEmpty(checkedAddress)) {
            return this.fail('请选择收货地址');
        }
        // 获取要购买的商品
        const checkedGoodsList = await this.model('cart').where({
            user_id: userId,
            checked: 1,
            is_delete: 0
        }).select();
        if (think.isEmpty(checkedGoodsList)) {
            return this.fail('请选择商品');
        }
        let checkPrice = 0;
        let checkStock = 0;
        for(const item of checkedGoodsList){
            let product = await this.model('product').where({
                id:item.product_id
            }).find();
            if(item.number > product.goods_number){
                checkStock++;
            }
            if(item.retail_price != item.add_price){
                checkPrice++;
            }
        }
        if(checkStock > 0){
            return this.fail(400, '库存不足，请重新下单');
        }
        if(checkPrice > 0){
            return this.fail(400, '价格发生变化，请重新下单');
        }
        // 获取订单使用的红包
        // 如果有用红包，则将红包的数量减少，当减到0时，将该条红包删除
        // 统计商品总价
        let goodsTotalPrice = 0.00;
        for (const cartItem of checkedGoodsList) {
            goodsTotalPrice += cartItem.number * cartItem.retail_price;
        }
        // 订单价格计算
        const orderTotalPrice = goodsTotalPrice + freightPrice; // 订单的总价
        const actualPrice = orderTotalPrice - 0.00; // 减去其它支付的金额后，要实际支付的金额 比如满减等优惠
        const currentTime = parseInt(new Date().getTime() / 1000);
        let print_info = '';
        for (const item in checkedGoodsList) {
            let i = Number(item) + 1;
            print_info = print_info + i + '、' + checkedGoodsList[item].goods_aka + '【' + checkedGoodsList[item].number + '】 ';
        }

        // const checkedAddress = await this.model('address').where({id: addressId}).find();
        const orderInfo = {
            order_sn: this.model('order').generateOrderNumber(),
            user_id: userId,
            // 收货地址和运费
            consignee: checkedAddress.name,
            mobile: checkedAddress.mobile,
            province: checkedAddress.province_id,
            city: checkedAddress.city_id,
            district: checkedAddress.district_id,
            address: checkedAddress.address,
            order_status: 101, // 订单初始状态为 101
            // 根据城市得到运费，这里需要建立表：所在城市的具体运费
            freight_price: freightPrice,
            postscript: postscript,
            add_time: currentTime,
            goods_price: goodsTotalPrice,
            order_price: orderTotalPrice,
            actual_price: actualPrice,
            change_price: actualPrice,
            print_info: print_info,
            offline_pay:offlinePay
        };
        // 开启事务，插入订单信息和订单商品
        const orderId = await this.model('order').add(orderInfo);
        orderInfo.id = orderId;
        if (!orderId) {
            return this.fail('订单提交失败');
        }
        // 将商品信息录入数据库
        const orderGoodsData = [];
        for (const goodsItem of checkedGoodsList) {
            orderGoodsData.push({
                user_id: userId,
                order_id: orderId,
                goods_id: goodsItem.goods_id,
                product_id: goodsItem.product_id,
                goods_name: goodsItem.goods_name,
                goods_aka: goodsItem.goods_aka,
                list_pic_url: goodsItem.list_pic_url,
                retail_price: goodsItem.retail_price,
                number: goodsItem.number,
                goods_specifition_name: goodsItem.goods_specifition_name,
            });
        }
        await this.model('order_goods').addMany(orderGoodsData);
        await this.model('cart').clearBuyGoods();
        return this.success({
            orderInfo: orderInfo
        });
    }

    /**
     * 创建订单接口
     * @returns {Promise.<void>}
     */
    async createOrderAction() {
        const userId = this.getLoginUserId();
        const userAgent = this.getUserAgent();
        const addressId = this.post('addressId');
        const offlinePay = this.post('offlinePay');
        const postscript = this.post('postscript') || '';
        const remark = this.post('remark') || '';
        const goodsList = this.post('goodsList');// 0：购物车下单，1:立即购买，2:再来一单
        const couponCode = this.post('couponCode') || ''; // 新增：优惠券码
        
        const addType = this.post('addType'); 
        const orderType = this.post('orderType');
      
        const goodsModel = this.model('goods');
        const addressModel = this.model('address');
      
        // 校验商品、库存、价格、地址及运费
        const checkGoods = await this.service('goods')
          .checkGoodsOrder(goodsList, addressId, userId, userAgent, goodsModel, addressModel);
      
        const goodsTotalPrice = parseFloat(checkGoods.goodsTotalPrice); // 商品总价
        const freightPrice = parseFloat(checkGoods.freightPrice); // 运费
        let actualPrice = goodsTotalPrice + freightPrice; // 实际支付金额
      
        const checkedGoodsList = checkGoods.checkedGoodsList; // 校验后的商品列表
        const checkedAddress = checkGoods.checkedAddress; // 校验后的地址

        // 优惠券处理
        let couponDiscount = 0;
        let couponInfo = null;
        
        if (couponCode) {
          const couponService = this.service('coupon');
          const validationResult = await couponService.validateCoupon(couponCode, userId, actualPrice);
          
          if (!validationResult.valid) {
            return this.fail(validationResult.message);
          }
          
          couponDiscount = validationResult.discountAmount;
          couponInfo = validationResult.coupon;
          actualPrice = Math.max(0, actualPrice - couponDiscount); // 确保金额不为负数
        }
      
        const currentTime = parseInt(Date.now() / 1000);
        const orderModel = this.model('order');
        const orderGoodsModel = this.model('order_goods');
        const cartModel = this.model('cart');
      
        // 构建打印信息
        let print_info = '';
        for (let i = 0; i < checkedGoodsList.length; i++) {
          const item = checkedGoodsList[i];
          let num = goodsList.filter(goods => goods.goodsId == item.goods_id && goods.productId == item.product_id);
          if (num.length > 0 && num[0].number) {
            print_info += `${i + 1}、${item.goods_name}【型号：${item.goods_specifition_name}】【数量：${num[0].number}】 `;
          }else{
            print_info += `${i + 1}、${item.goods_name}【型号：${item.goods_specifition_name}】 `;
          }
        }
      
        const orderInfo = {
          order_sn: orderModel.generateOrderNumber(),
          user_id: userId,
          consignee: checkedAddress.name,
          mobile: checkedAddress.mobile,
          province: checkedAddress.province_id,
          city: checkedAddress.city_id,
          district: checkedAddress.district_id,
          address: checkedAddress.address,
          order_status: 101,
          freight_price: freightPrice,
          postscript: postscript,
          add_time: currentTime,
          goods_price: goodsTotalPrice,
          order_price: goodsTotalPrice + freightPrice, // 订单原价
          actual_price: actualPrice, // 实际支付金额
          change_price: actualPrice,
          print_info: print_info,
          offline_pay: offlinePay,
          order_type: orderType,
          address_id: addressId,
          remark: remark,
          coupon_code: couponCode, // 新增：记录使用的优惠券码
          coupon_discount: couponDiscount, // 新增：记录优惠券折扣金额
          coupon_template_id: think.isEmpty(couponInfo) ? 0 : couponInfo.coupon_template_id // 新增：记录优惠券ID
        };
      
        const insertIdRes = await this.transaction("order", async (session) => {
          // 插入订单
          const orderId = await orderModel.db(session).add(orderInfo);
          if (!orderId) throw new Error('订单创建失败');
      
          // 插入订单商品
          const orderGoodsData = checkedGoodsList.map(item => {
            return {
                user_id: userId,
                order_id: orderId,
                goods_id: item.goods_id,
                product_id: item.product_id,
                goods_name: item.goods_name,
                goods_aka: item.product_name,
                goods_sn: item.goods_sn,
                list_pic_url: item.list_pic_url,
                retail_price: userAgent ? item.agent_price : item.retail_price,
                number: item.buy_number,
                goods_specifition_name: item.goods_specifition_name,
              }
          });
          await orderGoodsModel.db(session).addMany(orderGoodsData);
          
          // 使用优惠券
          if (couponCode) {
            const couponService = this.service('coupon');
            const useResult = await couponService.useCoupon(couponCode, userId, orderId);
            if (!useResult) {
              throw new Error('优惠券使用失败');
            }
          }
          
          if (addType == 0) {
            // 提取当前下单商品的 product_id 集合
            const productIds = checkedGoodsList.map(item => item.product_id);

            // 清除购物车中本次已下单的商品
            await cartModel.db(session)
            .where({ user_id: userId, product_id: ['IN', productIds] })
            .update({ is_delete: 1 });
          }
          
          return orderId;
        });
      
        // 返回创建好的订单信息
        return this.success({
            id: insertIdRes,
            order_sn: orderInfo.order_sn,
            actual_price: actualPrice,
            freight_price: freightPrice,
            goods_price: goodsTotalPrice,
            coupon_discount: couponDiscount,
            coupon_info: couponInfo ? {
              name: couponInfo.template_name,
              type: couponInfo.type,
              discount: couponInfo.discount
            } : null
        });
    }


    async updateAction() {
        const addressId = this.post('addressId');
        const orderId = this.post('orderId');
        // 备注
        // let postscript = this.post('postscript');
        // const buffer = Buffer.from(postscript);
        const updateAddress = await this.model('address').where({
            id: addressId
        }).find();
        const currentTime = parseInt(new Date().getTime() / 1000);
        const orderInfo = {
            // 收货地址和运费
            consignee: updateAddress.name,
            mobile: updateAddress.mobile,
            province: updateAddress.province_id,
            city: updateAddress.city_id,
            district: updateAddress.district_id,
            address: updateAddress.address,
            // TODO 根据地址计算运费
            // freight_price: 0.00,
            // add_time: currentTime
        };
        const updateInfo = await this.model('order').where({
            id: orderId
        }).update(orderInfo);
        return this.success(updateInfo);
    }

    /************* 新物流查询 ********** */
    async getOrderExpressAction() {
        const orderId = this.get('orderId');
        const mobile = this.get('mobile');
        const latestExpressInfo = await this.model('order_express').queryExpress100(orderId, mobile, 1);
        const expressObj = JSON.parse(latestExpressInfo.traces);

        return this.success({
          request_time: moment.unix(latestExpressInfo.request_time).format('YYYY-MM-DD HH:mm:ss'),
          is_finish: latestExpressInfo.is_finish,
          traces: expressObj.data || JSON.parse(latestExpressInfo.traces),
          logistic_code: latestExpressInfo.logistic_code,
          shipper_name: latestExpressInfo.shipper_name,
        });
    }
    // 查询退款订单物流信息
    async getRefundExpressAction() {
        const orderId = this.get('orderId');
        if (think.isEmpty(orderId)) {
            return this.fail('参数错误');
        }
        const expressInfo = await this.model('order_express').where({
            order_id: orderId,
            express_type: 2,
            is_delete: 0
        }).find();
        if (think.isEmpty(expressInfo)) {
            return this.success({
                traces: [],
                logistic_code: "",
            });
        }
        const latestExpressInfo = await this.model('order_express').queryExpress100(orderId, expressInfo.sender_phone, 2);
        const expressObj = JSON.parse(latestExpressInfo.traces);

        return this.success({
          request_time: moment.unix(latestExpressInfo.request_time).format('YYYY-MM-DD HH:mm:ss'),
          is_finish: latestExpressInfo.is_finish,
          traces: expressObj.data || JSON.parse(latestExpressInfo.traces),
          logistic_code: latestExpressInfo.logistic_code,
          shipper_name: latestExpressInfo.shipper_name,
        });
    }

    /**
     * 获取订单可用的优惠券列表
     */
    async getAvailableCouponsAction() {
        const userId = this.getLoginUserId();
        const orderAmount = parseFloat(this.post('orderAmount')) || 0;
        
        if (orderAmount <= 0) {
            return this.fail('订单金额必须大于0');
        }
        
        const couponService = this.service('coupon');
        const availableCoupons = await couponService.getAvailableCouponsForOrder(userId, orderAmount);
        
        return this.success(availableCoupons);
    }

    /**
     * 验证优惠券
     */
    async validateCouponAction() {
        const userId = this.getLoginUserId();
        const couponCode = this.post('couponCode');
        const orderAmount = parseFloat(this.post('orderAmount')) || 0;
        
        if (!couponCode) {
            return this.fail('优惠券码不能为空');
        }
        
        if (orderAmount <= 0) {
            return this.fail('订单金额必须大于0');
        }
        
        const couponService = this.service('coupon');
        const validationResult = await couponService.validateCoupon(couponCode, userId, orderAmount);
        
        if (!validationResult.valid) {
            return this.fail(validationResult.message);
        }
        
        return this.success({
            coupon: validationResult.coupon,
            discountAmount: validationResult.discountAmount,
            finalAmount: parseFloat(orderAmount - validationResult.discountAmount).toFixed(2)
        });
    }
};