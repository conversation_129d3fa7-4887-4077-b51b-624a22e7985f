const Base = require('../../common/controller/base.js');
const moment = require('moment');
module.exports = class extends Base {
    async indexAction() {
        const model = this.model('goods');
        const goodsList = await model.select();
        return this.success(goodsList);
    }
    /**
     * 商品详情页数据
     * @returns {Promise.<Promise|PreventPromise|void>}
     */
    async detailAction() {
        const goodsId = this.get('id');
        const userId = this.getLoginUserId();
        const userAgent = this.getUserAgent();
        const model = this.model('goods');
        let info = await model.where({
            id: goodsId,
            is_delete: 0
        }).find();
        if (think.isEmpty(info)) {
            return this.fail('该商品不存在或已下架');
        }
        const gallery = await this.model('goods_gallery').where({
            goods_id: goodsId,
            is_delete: 0,
        }).order('sort_order').limit(6).select();
        await this.model('footprint').addFootprint(userId, goodsId);
        let productList = await model.getProductList(goodsId);
        let goodsNumber = 0;
        for (let item of productList) {
            if (item.goods_number > 0) {
                goodsNumber = goodsNumber + item.goods_number;
            }
            if (userAgent && !think.isEmpty(item.agent_price)) {
                item.real_price = item.agent_price;
            } else {
                item.real_price = item.retail_price;
            }
        }
        // 如果是代理商，则显示代理商价格
        if (userAgent && !think.isEmpty(info.min_agent_price)) {
            info.min_real_price = info.min_agent_price;
        } else {
            info.min_real_price = info.min_retail_price;
        }
        if (userAgent && !think.isEmpty(info.agent_price)) {
            info.real_price = info.agent_price;
        } else {
            info.real_price = info.retail_price;
        }
        info.goods_number = goodsNumber;
        return this.success({
            info: info,
            gallery: gallery,
            productList: productList
        });
    }
    async goodsShareAction() {
        const goodsId = this.get('id');
        const info = await this.model('goods').where({
            id: goodsId
        }).field('name,retail_price').find();
        return this.success(info);
    }
    /**
     * 获取商品列表
     * @returns {Promise.<*>}
     */
    async listAction() {
        const userId = this.getLoginUserId();
        const userAgent = this.getUserAgent();
        const keyword = this.get('keyword');
        const sort = this.get('sort');
        const order = this.get('order');
        const sales = this.get('sales');
        const model = this.model('goods');
        const whereMap = {
            is_on_sale: 1,
            is_delete: 0,
        };
        if (!think.isEmpty(keyword)) {
            whereMap.name = ['like', `%${keyword}%`];
            // 添加到搜索历史
            await this.model('search_history').add({
                keyword: keyword,
                user_id: userId,
            });
            //    TODO 之后要做个判断，这个词在搜索记录中的次数，如果大于某个值，则将他存入keyword
        }
        // 排序
        let orderMap = {};
        if (sort === 'price') {
            // 按价格
            orderMap = {
                retail_price: order
            };
        } else if (sort === 'sales') {
            // 按价格
            orderMap = {
                sell_volume: sales
            };
        } else {
            // 按商品添加时间
            orderMap = {
                sort_order: 'asc'
            };
        }
        const goodsData = await model.where(whereMap).order(orderMap).select();
        for (let goodsItem of goodsData) {
            // 如果是代理商，则显示代理商价格
            if (userAgent && !think.isEmpty(goodsItem.min_agent_price)) {
                goodsItem.min_real_price = goodsItem.min_agent_price;
            } else {
                goodsItem.min_real_price = goodsItem.min_retail_price;
            }
            if (userAgent && !think.isEmpty(goodsItem.agent_price)) {
                goodsItem.real_price = goodsItem.agent_price;
            } else {
                goodsItem.real_price = goodsItem.retail_price;
            }
        }
        return this.success(goodsData);
    }
    /**
     * 在售的商品总数
     * @returns {Promise.<Promise|PreventPromise|void>}
     */
    async countAction() {
        const goodsCount = await this.model('goods').where({
            is_delete: 0,
            is_on_sale: 1
        }).count('id');
        return this.success({
            goodsCount: goodsCount
        });
    }

    /**
     * 查询商品是否可以正常下单，并且返回当前配置的快递费用和可用的运费模板
     * addressId（默认0)
     * goodsList [{goodsId（商品id）; productId (商品类型id) number (个数)}]
     */
    async checkGoodsAction() {
        try {
            const userId = this.getLoginUserId();
            const userAgent = this.getUserAgent();
            const addressId = this.post('addressId');
            const goodsList = this.post('goodsList');
            const orderId = this.post('orderId');

            // 验证商品列表
            if (think.isEmpty(goodsList) || !Array.isArray(goodsList)) {
                return this.fail('商品列表不能为空');
            }

            // 验证商品列表格式
            for (const goods of goodsList) {
                if (!goods.goodsId || !goods.productId || !goods.number || goods.number <= 0) {
                    return this.fail('商品信息格式错误');
                }
            }

            let orderInfo = null;
            if (orderId > 0) {
                // 查出订单信息
                orderInfo = await this.model('order').where({
                    id: orderId
                }).find();
                if (think.isEmpty(orderInfo)) {
                    return this.fail('订单信息错误');
                }
            }

            const goodsModel = this.model('goods');
            const addressModel = this.model('address');

            // // 如果是查询已有订单，使用原有逻辑
            // if (!think.isEmpty(orderInfo)) {
            //     const checkRes = await this.service('goods')
            //         .checkGoodsOrder(
            //             goodsList, addressId,
            //             userId, userAgent,
            //             goodsModel, addressModel
            //         );

            //     checkRes.goodsTotalPrice = orderInfo.goods_price;
            //     checkRes.freightPrice = orderInfo.freight_price;

            //     return this.success({
            //         goodsTotalPrice: checkRes.goodsTotalPrice,
            //         freightPrice: checkRes.freightPrice,
            //         checkedAddress: checkRes.checkedAddress,
            //         checkedGoodsList: checkRes.checkedGoodsList,
            //     });
            // }

            // 新逻辑：获取所有可用的运费模板
            const result = await this.service('goods').getAvailableFreightTemplates(
                goodsList,
                addressId,
                userId,
                userAgent,
                goodsModel,
                addressModel
            );

            // 为了保持向后兼容，同时返回默认运费（最便宜的）
            const defaultFreightPrice = result.freightTemplates.length > 0
                ? result.freightTemplates[0].freight_price
                : 0;

            return this.success({
                goodsTotalPrice: result.goodsTotalPrice,
                freightPrice: defaultFreightPrice.toFixed(2), // 默认运费（最便宜的）
                checkedAddress: result.checkedAddress,
                checkedGoodsList: result.checkedGoodsList,
                freightTemplates: result.freightTemplates // 新增：所有可用的运费模板
            });
        } catch (error) {
            think.logger.error('检查商品下单信息失败:', error);
            return this.fail(error.message || '检查商品信息失败');
        }
    }
};