/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-13 23:51:51
 * @FilePath: /petshop-server/src/api/controller/catalog.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
module.exports = class extends Base {
    /**
     * 获取分类栏目数据
     * @returns {Promise.<Promise|void|PreventPromise>}
     */
    async indexAction() {
        const categoryId = this.get('id');
        const model = this.model('category');
        const data = await model.where({
            parent_id: 0,
            is_category: 1
        }).order('sort_order ASC').select();
        // let currentCategory = null;
        // if (categoryId) {
        //     currentCategory = await model.where({
        //         'id': categoryId
        //     }).find();
        // }
        // if (think.isEmpty(currentCategory)) {
        //     currentCategory = data[0];
        // }
        return this.success({
            categoryList: data,
        });
    }
    async currentAction() {
        const categoryId = this.get('id');
        let data = await this.model('category').where({
            id: categoryId
        }).field('id,name,img_url,p_height').find();
        return this.success(data);
    }
    async currentlistAction() {
        const page = this.post('page');
        const size = this.post('size');
        const categoryId = this.post('id');
        const brand_id = this.post('brand_id');
        const userAgent = this.getUserAgent();
        let where = {
            is_on_sale: 1,
            is_delete: 0
        };
        if (categoryId) {
            where.category_id = categoryId;
        }
        if (brand_id) {
            where.brand_id = brand_id;
        }
        let list = await this.model('goods').where(where).order({
            sort_order: 'asc'
        }).field('name,id,goods_brief,brand_id,min_retail_price,retail_price,min_market_price,agent_price,min_agent_price,list_pic_url,goods_number').page(page, size).countSelect();
        for (let goodsItem of list.data) {
            // 如果是代理商，则显示代理商价格
            if (userAgent && !think.isEmpty(goodsItem.min_agent_price)) {
                goodsItem.min_real_price = goodsItem.min_agent_price;
            }else{
                goodsItem.min_real_price = goodsItem.min_retail_price;
            }
            if (userAgent && !think.isEmpty(goodsItem.agent_price)) {
                goodsItem.real_price = goodsItem.agent_price;
            }else{
                goodsItem.real_price = goodsItem.retail_price;
            }
        }
        return this.success(list);
    }

    // ***************** 商品品牌 ****************//
    // 查出当前商品类别下所有商品所存在的品牌
    async getGoodsBrandByCategoryIdAction() {
        const categoryId = this.get('category_id');

        let brand_id_data = []
        if (categoryId == 0) {
            brand_id_data = await this.model('goods').where({
                is_delete: 0,
                is_on_sale: 1
            }).distinct('brand_id').getField('brand_id');
        }else{
            brand_id_data = await this.model('goods').where({
                category_id: categoryId,
                is_delete: 0,
                is_on_sale: 1
            }).distinct('brand_id').getField('brand_id');
        }

        if (think.isEmpty(brand_id_data)) {
            return this.success([
                {
                    id: 0,
                    brand_name: '其他'
                }
            ]);
        }
        const brandData = await this.model('goods_brand').where({
            id: ['IN', brand_id_data],
            is_delete: 0
        }).select();
        if (brand_id_data.indexOf(0) > -1) {
            brandData.push({
                id: 0,
                brand_name: '其他'
            });
        }
        
        return this.success(brandData);
    }
};