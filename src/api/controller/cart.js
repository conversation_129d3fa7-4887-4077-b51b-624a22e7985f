const Base = require('../../common/controller/base.js');
const moment = require('moment');
const pinyin = require("pinyin");
module.exports = class extends Base {
    
    async getCart() {
        const userId = this.getLoginUserId();
        const userAgent = this.getUserAgent(); // 判断是否代理用户
        const cartModel = this.model('cart');

        // 1. 获取购物车中未删除的全部条目（不聚合）
        const cartList = await cartModel.where({ user_id: userId, is_delete: 0 }).select();

        if (think.isEmpty(cartList)) {
            return {
                cartList: [],
                cartTotal: {
                    goodsCount: 0,
                    goodsAmount: '0.00',
                    checkedGoodsCount: 0,
                    checkedGoodsAmount: '0.00',
                    user_id: userId,
                    numberChange: 0
                }
            };
        }

        // 2. 获取对应的 product + goods 数据
        const productIds = cartList.map(item => item.product_id);
        const productList = await this.model('product')
            .alias('p')
            .join({
            table: 'goods',
            join: 'left',
            as: 'g',
            on: ['p.goods_id', 'g.id']
            })
            .field('p.*, g.name as goods_name, g.list_pic_url')
            .where({ 'p.id': ['IN', productIds], 'p.is_delete': 0 })
            .select();

        // 3. 转换为 Map，提高查找效率
        const productMap = {};
        for (const p of productList) {
            productMap[p.id] = p;
        }

        // 4. 初始化统计变量
        let goodsCount = 0;
        let goodsAmount = 0;
        let checkedGoodsCount = 0;
        let checkedGoodsAmount = 0;
        let numberChange = 0;

        // 需要更新的购物车项（分批处理）
        const needSoftDeleteIds = [];
        const needUncheckIds = [];
        const needUpdateQuantities = [];

        // 5. 遍历购物车项处理逻辑
        for (const cartItem of cartList) {
            const product = productMap[cartItem.product_id];

            // 产品不存在，标记删除
            if (!product) {
                needSoftDeleteIds.push(cartItem.id);
                continue;
            }

            const realPrice = userAgent ? product.agent_price : product.retail_price;
            const productNum = product.goods_number;

            // 商品售罄或下架，标记未选中
            if (productNum <= 0 || product.is_on_sale === 0) {
                if (cartItem.checked === 1) {
                    needUncheckIds.push(cartItem.id);
                }
                cartItem.number = 0;
            } else if (productNum < cartItem.number) {
                // 库存不足，调整数量
                cartItem.number = productNum;
                numberChange = 1;
                needUpdateQuantities.push({ id: cartItem.id, number: productNum });
            } else if (cartItem.number === 0) {
                // 有库存但数量为 0，默认设置为 1
                cartItem.number = 1;
                numberChange = 1;
                needUpdateQuantities.push({ id: cartItem.id, number: 1 });
            }

            // 累加购物车总数与金额
            goodsCount += cartItem.number;
            goodsAmount += cartItem.number * realPrice;

            // 商品额外信息赋值
            cartItem.real_price = realPrice;
            cartItem.list_pic_url = product.list_pic_url || '';
            cartItem.goods_name = product.goods_name || '';
            cartItem.goods_specifition_name = product.goods_specifition_name || '';
            cartItem.goods_number = product.goods_number;
            cartItem.weight_count = cartItem.number * (product.goods_weight || 0);
            cartItem.is_on_sale = product.is_on_sale;
            cartItem.market_price = product.market_price;

            // 选中商品统计
            if (cartItem.checked && productNum > 0) {
                checkedGoodsCount += cartItem.number;
                checkedGoodsAmount += cartItem.number * realPrice;
            }
        }

        // 6. 批量更新数据库
        if (needSoftDeleteIds.length > 0) {
            await cartModel.where({ id: ['IN', needSoftDeleteIds] }).update({ is_delete: 1 });
        }
        if (needUncheckIds.length > 0) {
            await cartModel.where({ id: ['IN', needUncheckIds] }).update({ checked: 0 });
        }
        for (const item of needUpdateQuantities) {
            await cartModel.where({ id: item.id }).update({ number: item.number });
        }

        return {
            cartList,
            cartTotal: {
                goodsCount,
                goodsAmount: goodsAmount.toFixed(2),
                checkedGoodsCount,
                checkedGoodsAmount: checkedGoodsAmount.toFixed(2),
                user_id: userId,
                numberChange
            }
        };
    }
    /**
     * 获取购物车信息，所有对购物车的增删改操作，都要重新返回购物车的信息
     * @return {Promise} []
     */
    async indexAction() {
        return this.success(await this.getCart());
    }

    /**
     * 添加商品到购物车
     * @returns {Promise.<*>}
     */
    async addAction() {
        const goodsId = this.post('goodsId');
		const userId = this.getLoginUserId();;
        const productId = this.post('productId');
        const number = this.post('number');
        const currentTime = parseInt(new Date().getTime() / 1000);
        // 判断商品是否可以购买
        const goodsInfo = await this.model('goods').where({
            id: goodsId,
            is_delete: 0,
            is_on_sale: 1
        }).find();
        if (think.isEmpty(goodsInfo)) {
            return this.fail(400, '商品已下架');
        }
        // 取得规格的信息,判断规格库存
        const productInfo = await this.model('product').where({
            id: productId,
            is_delete: 0,
            is_on_sale: 1
        }).find();
        if (think.isEmpty(productInfo)) {
            return this.fail(400, '规格已下架');
        }
        // 查询当前购物车中是否已经存在此商品
        const cartInfo = await this.model('cart').where({
            user_id: userId,
            product_id: productId,
            is_delete: 0
        }).find();
        // 如果存在则增加个数，不存在则添加数据
        if (!think.isEmpty(cartInfo)) {
            if (productInfo.goods_number < (number + cartInfo.number)) {
                return this.fail(400, '库存不足');
            }
            await this.model('cart').where({
                user_id: userId,
                product_id: productId,
                is_delete: 0,
                id: cartInfo.id
            }).update({
                number: cartInfo.number + number
            });
        }else{
            if (productInfo.goods_number < number) {
                return this.fail(400, '库存不足');
            }

            let retail_price = this.getUserAgent() ? productInfo.agent_price : productInfo.retail_price;
            // 添加到购物车
            const cartData = {
                goods_id: productInfo.goods_id,
                product_id: productId,
                goods_sn: productInfo.goods_sn,
                goods_name: goodsInfo.name,
                goods_aka: productInfo.goods_name,
                goods_weight: productInfo.goods_weight,
                freight_template_id: goodsInfo.freight_template_id || 0,
                list_pic_url: goodsInfo.list_pic_url,
                number: number,
                user_id: userId,
                add_price: retail_price,
                goods_specifition_name: productInfo.goods_specifition_name,
                add_time: currentTime,
            };
            await this.model('cart').add(cartData);
        }
        return this.success(await this.getCart());
    }
    /**
     * 删除购物车商品数量
     * @returns {Promise.<*>}
     */
    async reduceAction() {
        const productId = this.post('productId');
		const userId = this.getLoginUserId();;
        const number = this.post('number');
        if (think.isEmpty(productId) || think.isEmpty(number)) {
            return this.fail('删除出错');
        }
        // 减少商品数量，当个数为0 时候，删除此商品
        await this.model('cart').where({
            product_id: productId,
            user_id: userId,
            is_delete: 0
        }).decrement('number', number);

        await this.model('cart').where({
            product_id: productId,
            user_id: userId,
            is_delete: 0,
            number: 0
        }).update({
            is_delete: 1
        });
        return this.success(await this.getCart(0));
    }
    // 更新指定的购物车信息
    async updateAction() {
        const productId = this.post('productId'); // 新的product_id
        const id = this.post('id'); // cart.id
        const number = parseInt(this.post('number')); // 不是
        if (think.isEmpty(productId) || think.isEmpty(id) || think.isEmpty(number)) {
            return this.fail(400, '参数错误');
        }
        // 取得规格的信息,判断规格库存
        const productInfo = await this.model('product').where({
            id: productId,
            is_delete: 0,
        }).find();
        if (think.isEmpty(productInfo) || productInfo.goods_number < number) {
            return this.fail(400, '库存不足');
        }
        // 判断是否已经存在product_id购物车商品
        const cartInfo = await this.model('cart').where({
            id: id,
            is_delete: 0
        }).find();
        if (think.isEmpty(cartInfo)) {
            return this.fail(400, '购物车商品不存在');
        }
        // 只是更新number
        await this.model('cart').where({
            id: id,
            is_delete: 0
        }).update({
            number: number
        });
        return this.success(await this.getCart(0));
    }
    // 是否选择商品，如果已经选择，则取消选择，批量操作
    // todo: 这个接口要改造
    async checkedAction() {
		const userId = this.getLoginUserId();;
        let productId = this.post('productIds').toString();
        const isChecked = this.post('isChecked');
        if (think.isEmpty(productId)) {
            return this.fail('删除出错');
        }
        productId = productId.split(',');
        await this.model('cart').where({
            product_id: {
                'in': productId
            },
            user_id: userId,
            is_delete: 0
        }).update({
            checked: parseInt(isChecked)
        });
        return this.success(await this.getCart(0));
    }
    // 删除选中的购物车商品，批量删除
    async deleteAction() {
        let productId = this.post('productIds');
		const userId = this.getLoginUserId();;
        if (think.isEmpty(productId)) {
            return this.fail('删除出错');
        }
        await this.model('cart').where({
            product_id: productId,
            user_id: userId,
            is_delete: 0
        }).update({
            is_delete: 1
        });
        return this.success(await this.getCart(0));
    }
    // 获取购物车商品的总件件数
    async goodsCountAction() {
        return this.success(await this.getCart());
    }
};