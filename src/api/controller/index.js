/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-29 23:59:10
 * @FilePath: /petshop-server/src/api/controller/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
// const view = require('think-view');
const moment = require('moment');
const rp = require('request-promise');
const http = require("http");

module.exports = class extends Base {
    async indexAction() {
        //auto render template file index_index.html
        return this.display();
    }
    async appInfoAction() {
		const userId = this.getLoginUserId();
		const userAgent = this.getUserAgent();

        const banner = await this.model('ad').where({
            enabled: 1,
            is_delete: 0
        }).field('link_type,goods_id,image_url,link').order('sort_order asc').select();
        const notice = await this.model('notice').where({
            is_delete: 0
        }).field('content').select();
        const channel = await this.model('category').where({
            is_channel: 1,
            parent_id: 0,
        }).field('id,icon_url,name,sort_order').order({
            sort_order: 'asc'
        }).select();
        // 添加获取置顶分类
        const topCategory = await this.model('category').where({
            is_top: 1,
            parent_id: 0,
        }).field('id,icon_url,name,sort_order,img_url').order({
            sort_order: 'asc'
        }).select();
        const categoryList = await this.model('category').where({
            parent_id: 0,
            is_show: 1
        }).field('id,name,img_url as banner, p_height as height').order({
            sort_order: 'asc'
        }).select();
		for (const categoryItem of categoryList) {
			let categoryGoods = await this.model('goods').where({
				category_id: categoryItem.id,
				goods_number: ['>=', 0],
				is_on_sale: 1,
				is_index: 1,
				is_delete: 0
			}).field('id,list_pic_url,is_new,goods_number,name,min_retail_price,retail_price,min_market_price,min_agent_price,agent_price').order({
				sort_order: 'asc'
			}).select();

			for (let goodsItem of categoryGoods) {
				// 如果是代理商，则显示代理商价格
				if (userAgent && !think.isEmpty(goodsItem.min_agent_price)) {
					goodsItem.min_real_price = goodsItem.min_agent_price;
				}else{
					goodsItem.min_real_price = goodsItem.min_retail_price;
				}
				if (userAgent && !think.isEmpty(goodsItem.agent_price)) {
					goodsItem.real_price = goodsItem.agent_price;
				}else{
					goodsItem.real_price = goodsItem.retail_price;
				}
			}
			
			categoryItem.goodsList = categoryGoods;
		}
		
		let cartCount = 0;
		if (!think.isEmpty(userId)) {
			cartCount = await this.model('cart').where({
				user_id: userId,
				is_delete: 0
			}).sum('number');
		}
		let data = {
			channel: channel,
			topCategory: topCategory, // 添加置顶分类数据
			banner: banner,
			notice: notice,
			categoryList: categoryList,
			cartCount: cartCount,
		}
        return this.success(data);
    }
	
	async checkUpdateAction() {
		const appVersion = this.post('appVersion');
		const platform = this.post('platform');
		const rnVersion = this.post('rnVersion');

		const update = await this.model('app_update').where({
			platform: platform,
			is_delete: 0
		}).order('id desc').find();

		let appNeedUpdate = 0;
		let rnNeedUpdate = 0;
		let isForce = 0;
		let isAlert = 0;
		
		if (!think.isEmpty(update)) {

			function compareVersion(v1, v2) {
				const arr1 = v1.split('.').map(Number);
				const arr2 = v2.split('.').map(Number);
				const len = Math.max(arr1.length, arr2.length);
				for (let i = 0; i < len; i++) {
					const n1 = arr1[i] || 0;
					const n2 = arr2[i] || 0;
					if (n1 > n2) return 1;
					if (n1 < n2) return -1;
				}
				return 0;
			}
			
			// 判断APP 版本号是否大于当前版本号
			if (update.app_version && appVersion && compareVersion(update.app_version, appVersion) > 0) {
				appNeedUpdate = 1;
				// 判断是否强制更新
				if (update.is_force == 1) {
					isForce = 1;
				}
				if (update.is_alert == 1) {
					isAlert = 1;
				}
			}
			// 当APP版本相同时候才判断RN 版本号是否大于当前版本号
			if (update.rn_version && rnVersion && update.app_version == appVersion && compareVersion(update.rn_version, rnVersion) > 0) {
				rnNeedUpdate = 1;
				// 判断是否强制更新
				if (update.is_force == 1) {
					isForce = 1;
				}
				if (update.is_alert == 1) {
					isAlert = 1;
				}
			}

			return this.success({
				appNeedUpdate: appNeedUpdate,
				rnNeedUpdate: rnNeedUpdate,
				appVersion: update.app_version,
				rnVersion: update.rn_version,
				isForce: isForce,
				isAlert: isAlert,
				rnUpdateUrl: update.bundle_url,
				appUpdateUrl: update.app_url,
				updateDesc: update.update_desc
			});
		}

		return this.success({
			appNeedUpdate: 0,
			rnNeedUpdate: 0,
			appVersion: appVersion,
			rnVersion: rnVersion,
			isForce: 0,
			isAlert: 0,
			rnUpdateUrl: "",
			appUpdateUrl: "",
			updateDesc: ""
		});
	}
};