/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-31 22:38:56
 * @FilePath: /petshop-server/src/api/controller/settings.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');;
module.exports = class extends Base {
  async showSettingsAction() {
    let info = await this.model("show_settings")
      .where({
        id: 1,
      })
      .find();
    return this.success(info);
  }
  async saveAction() {
    let userId = this.getLoginUserId();
    let name = this.post("name");
    let mobile = this.post("mobile");
    let nickName = this.post("nickName");
    let avatar = this.post("avatar");
    let name_mobile = 0;
    if (name != "" && mobile != "") {
      name_mobile = 1;
    }
    let data = {
      name: name,
      mobile: mobile,
      nickname: nickName,
      avatar: avatar,
      name_mobile: name_mobile,
    };
    let info = await this.model("user")
      .where({
        id: userId,
      })
      .update(data);
    // 清除缓存
    think.cache(`user_${id}`, null);
    return this.success(info);
  }
  async userDetailAction() {
    let userId = this.getLoginUserId();
    if (userId != 0) {
      let info = await this.model("user")
        .where({
          id: userId,
        })
        .field("id,mobile,name,nickname,avatar")
        .find();
      return this.success(info);
    }
    else{
      return this.fail(100,'未登录')
    }
  }
};
