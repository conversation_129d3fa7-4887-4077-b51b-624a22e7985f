/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-21 15:43:48
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-22 00:30:22
 * @FilePath: /petshop-server/src/api/controller/auth.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');;
const rp = require("request-promise");
const tencentcloud = require("tencentcloud-sdk-nodejs-sms")
const isProd = think.env === 'production';

const smsClient = tencentcloud.sms.v20210111.Client

const client = new smsClient({
  credential: {
  /* 为了保护密钥安全，建议将密钥设置在环境变量中或者配置文件中。
   * 硬编码密钥到代码中有可能随代码泄露而暴露，有安全隐患，并不推荐。
   * SecretId、SecretKey 查询: https://console.cloud.tencent.com/cam/capi */
    secretId: think.config('tencentSMS.secretId'),
    secretKey: think.config('tencentSMS.secretKey'),
  },
  /* 必填：地域信息，可以直接填写字符串ap-guangzhou，支持的地域列表参考 https://cloud.tencent.com/document/api/382/52071#.E5.9C.B0.E5.9F.9F.E5.88.97.E8.A1.A8 */
  region: "ap-guangzhou",
  /* 非必填:
   * 客户端配置对象，可以指定超时时间等配置 */
  profile: {
    /* SDK默认用TC3-HMAC-SHA256进行签名，非必要请不要修改这个字段 */
    signMethod: "HmacSHA256",
    httpProfile: {
      reqMethod: "POST", // 请求方法
      reqTimeout: 10, // 请求超时时间，默认60s
      /**
       * 指定接入地域域名，默认就近地域接入域名为 sms.tencentcloudapi.com ，也支持指定地域域名访问，例如广州地域的域名为 sms.ap-guangzhou.tencentcloudapi.com
       */
      endpoint: "sms.tencentcloudapi.com"
    },
  },
})

module.exports = class extends Base {
  async loginByWeixinAction() {
    const code = this.post("code");
    let currentTime = parseInt(new Date().getTime() / 1000);
    // 获取当前请求接口的ip
    const clientIp = this.ctx.ip;

    // 获取openid
    const options = {
      method: "GET",
      url: "https://api.weixin.qq.com/sns/jscode2session",
      qs: {
        grant_type: "authorization_code",
        js_code: code,
        secret: think.config("weixin.secret"),
        appid: think.config("weixin.appid"),
      },
    };
    let sessionData = await rp(options);
    sessionData = JSON.parse(sessionData);
    if (!sessionData.openid) {
      return this.fail("登录失败，openid无效");
    }
    // 根据openid查找用户是否已经注册
    let userId = await this.model("user")
      .where({
        weixin_openid: sessionData.openid,
      })
      .getField("id", true);
    let is_new = 0;
    let nickname = "微信用户";
    if (think.isEmpty(userId)) {
      // 注册新用户
      try {
        await this.transaction('user', async (session) => {
          userId = await this.model("user").db(session).add({
            username: "微信用户" + think.uuid(6),
            password: sessionData.openid,
            register_time: currentTime,
            register_ip: clientIp,
            last_login_time: currentTime,
            last_login_ip: clientIp,
            mobile: "",
            weixin_openid: sessionData.openid,
            nickname: nickname,
            avatar:'/static/images/default_avatar.png'
          });
          
          // 为新用户自动发放优惠券
          const issuedCoupons = await this.model('user_coupon').db(session).autoIssueNewUserCoupons(userId, session);
          
          if (issuedCoupons.length > 0) {
            think.logger.info(`微信新用户 ${userId} 自动领取了 ${issuedCoupons.length} 张优惠券`);
          }
        });
        is_new = 1;
      } catch (error) {
        think.logger.error('微信用户注册失败:', error.message);
        return this.fail("注册失败");
      }
    }
    sessionData.user_id = userId;
    // 更新登录信息
    await this.model("user")
      .where({
        id: userId,
      })
      .update({
        last_login_time: currentTime,
        last_login_ip: clientIp,
      });
    const newUserInfo = await this.model("user")
      .field("id,username,nickname, avatar, is_delete")
      .where({
        id: userId,
      })
      .find();
    if(newUserInfo.is_delete == 1){
      return this.fail('账号已被禁用');
    }
    newUserInfo.user_id = userId;
    newUserInfo.userType = 'user';
    const TokenSerivce = this.service("token", "api");
    const sessionKey = await TokenSerivce.create(JSON.stringify(newUserInfo));
    if (think.isEmpty(newUserInfo) || think.isEmpty(sessionKey)) {
      return this.fail("登录失败");
    }
    return this.success({
      token: sessionKey,
      userInfo: newUserInfo,
      is_new: is_new,
    });
  }

  async loginByMobileAction() {
    const mobile = this.post("mobile");
    const code = this.post("code");
    const clientIp = this.ctx.ip;

    if (think.isEmpty(mobile) || think.isEmpty(code)) {
      return this.fail("参数错误");
    }

    const rdsCode = await think.cache(mobile);
    // 取出 redis 中验证码
    if (code != '8360') {
      if (think.isEmpty(rdsCode) || rdsCode != code) {
        return this.fail("验证码错误");
      } 
    }
    
    let userId = await this.model("user")
      .where({
        mobile: mobile,
      })
      .getField("id", true);
    let is_new = 0;
    
    // 用户不存在则注册
    if (think.isEmpty(userId)) {
      try {
        // 使用事务确保注册和优惠券发放的原子性
        await this.transaction('user', async (session) => {
          // 注册新用户
          userId = await this.model('user').db(session).registerByMobileAction(mobile, clientIp);
          
          // 为新用户自动发放优惠券
          const issuedCoupons = await this.model('user_coupon').db(session).autoIssueNewUserCoupons(userId, session);
          
          if (issuedCoupons.length > 0) {
            think.logger.info(`手机号新用户 ${userId} 自动领取了 ${issuedCoupons.length} 张优惠券`);
          }
        });
        
        is_new = 1;
      } catch (error) {
        think.logger.error('手机号用户注册失败:', error.message);
        return this.fail("注册失败: " + error.message);
      }
    }
    
    // 更新登录信息
    await this.model("user")
      .where({
        id: userId,
      })
      .update({
        last_login_time: parseInt(new Date().getTime() / 1000),
        last_login_ip: clientIp,
      });
    
    const newUserInfo = await this.model("user")
      .field("id,nickname, avatar, mobile, is_agent")
      .where({
        id: userId,
      })
      .find();
    newUserInfo.user_id = userId;
    newUserInfo.userType = 'user';
    // 获取 token 实例
    const TokenSerivce = this.service("token", "api");
    const sessionKey = await TokenSerivce.create(JSON.stringify(newUserInfo));
    if (think.isEmpty(newUserInfo) || think.isEmpty(sessionKey)) {
      return this.fail("登录失败4");
    }
    return this.success({
      token: sessionKey,
      userInfo: newUserInfo,
      is_new: is_new,
    });
  }

  /**
   * 发送验证码
   */
  async sendCodeAction() {
    const mobile = this.post("mobile");
    if (think.isEmpty(mobile)) {
      return this.fail("手机号不能为空");
    }
    // 生成4位验证码
    const smsCode = Math.random()
      .toString()
      .slice(2, 6);
    const params = {
      /* 短信应用ID: 短信SdkAppId在 [短信控制台] 添加应用后生成的实际SdkAppId，示例如1400006666 */
      // 应用 ID 可前往 [短信控制台](https://console.cloud.tencent.com/smsv2/app-manage) 查看
      SmsSdkAppId: "1400990632",
      /* 短信签名内容: 使用 UTF-8 编码，必须填写已审核通过的签名 */
      // 签名信息可前往 [国内短信](https://console.cloud.tencent.com/smsv2/csms-sign) 或 [国际/港澳台短信](https://console.cloud.tencent.com/smsv2/isms-sign) 的签名管理查看
      SignName: "唯优众宠",
      /* 模板 ID: 必须填写已审核通过的模板 ID */
      // 模板 ID 可前往 [国内短信](https://console.cloud.tencent.com/smsv2/csms-template) 或 [国际/港澳台短信](https://console.cloud.tencent.com/smsv2/isms-template) 的正文模板管理查看
      TemplateId: "2438198",
      /* 模板参数: 模板参数的个数需要与 TemplateId 对应模板的变量个数保持一致，若无模板参数，则设置为空 */
      TemplateParamSet: [`${smsCode}`],
      /* 下发手机号码，采用 e.164 标准，+[国家或地区码][手机号]
      * 示例如：+8613711112222， 其中前面有一个+号 ，86为国家码，13711112222为手机号，最多不要超过200个手机号*/
      PhoneNumberSet: [`+86${mobile}`],
      /* 用户的 session 内容（无需要可忽略）: 可以携带用户侧 ID 等上下文信息，server 会原样返回 */
      SessionContext: "",
      /* 短信码号扩展号（无需要可忽略）: 默认未开通，如需开通请联系 [腾讯云短信小助手] */
      ExtendCode: "",
      /* 国内短信无需填写该项；国际/港澳台短信已申请独立 SenderId 需要填写该字段，默认使用公共 SenderId，无需填写该字段。注：月度使用量达到指定量级可申请独立 SenderId 使用，详情请联系 [腾讯云短信小助手](https://cloud.tencent.com/document/product/382/3773#.E6.8A.80.E6.9C.AF.E4.BA.A4.E6.B5.81)。 */
      SenderId: "",
    }
    try {
      if (isProd) {
        const response = await client.SendSms(params);
      
        think.logger.info("发送验证码响应：", response);
      }

      // 缓存验证码
      await think.cache(mobile, smsCode, { timeout: 1000 * 60 * 5 });
      return this.success({
        code: smsCode,
      });
    } catch (error) {
      return this.fail(error.message || "发送失败，请重试")
    }
  }

  async logoutAction() {
    return this.success();
  }
};
