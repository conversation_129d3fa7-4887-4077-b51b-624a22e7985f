/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-01 00:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-04 23:31:16
 * @FilePath: /petshop-server/src/api/controller/coupon.js
 * @Description: 优惠券相关接口
 */
const Base = require('../../common/controller/base.js');
const moment = require('moment');

module.exports = class extends Base {
  /**
   * 获取用户优惠券列表
   */
  async getUserCouponsAction() {
    const userId = this.getLoginUserId();
    const status = this.get('status') || 'valid'; // valid: 有效, used: 已使用, expired: 已过期

    let whereCondition = {
      'uc.user_id': userId,
      'uc.is_delete': 0
    };

    const now = moment().format('YYYY-MM-DD HH:mm:ss');

    switch (status) {
      case 'valid':
        whereCondition['uc.is_used'] = 0;
        whereCondition['uc.valid_start'] = ['<=', now];
        whereCondition['uc.valid_end'] = ['>=', now];
        break;
      case 'used':
        whereCondition['uc.is_used'] = 1;
        break;
      case 'expired':
        whereCondition['uc.is_used'] = 0;
        whereCondition['uc.valid_end'] = ['<', now];
        break;
    }

    const coupons = await this.model('user_coupon').alias('uc')
      .field('uc.*, ct.name as template_name, ct.type, ct.discount, ct.min_amount')
      .join({
        table: 'coupon_template',
        join: 'left',
        as: 'ct',
        on: ['ct.id', 'uc.coupon_template_id']
      })
      .where(whereCondition)
      .order('uc.create_time DESC')
      .select();

    return this.success(coupons);
  }

  /**
   * 手动领取优惠券
   */
  async claimCouponAction() {
    const userId = this.getLoginUserId();
    const templateIds = this.post('templateIds');

    if (!templateIds || !Array.isArray(templateIds) || templateIds.length === 0) {
      return this.fail('优惠券模板ID数组不能为空');
    }

    // 1. 事务外先过滤
    const userCouponModel = this.model('user_coupon');
    const couponTemplateModel = this.model('coupon_template');
    const validTemplateIds = [];

    for (const templateId of templateIds) {
      // 检查是否已领取
      const existing = await userCouponModel.where({
        user_id: userId,
        coupon_template_id: templateId,
        is_delete: 0
      }).find();
      if (!think.isEmpty(existing)) continue;

      // 检查剩余数量
      const hasRemaining = await couponTemplateModel.checkRemainingCount(templateId);
      if (!hasRemaining) continue;

      validTemplateIds.push(templateId);
    }

    if (validTemplateIds.length === 0) {
      return this.fail('没有可领取的优惠券');
    }

    // 2. 事务内只做插入
    try {
      const couponIds = [];
      await this.transaction('user_coupon', async (session) => {
        for (const templateId of validTemplateIds) {
          const couponId = await userCouponModel.db(session).issueCouponToUser(userId, templateId, '', session);
          couponIds.push(couponId);
        }
      });
      return this.success({ coupon_ids: couponIds }, '优惠券领取成功');
    } catch (error) {
      return this.fail(error.message);
    }
  }

  /**
   * 获取可领取的优惠券模板列表
   */
  async getAvailableTemplatesAction() {
    const userId = this.getLoginUserId();

    // 获取所有启用的优惠券模板
    const templates = await this.model('coupon_template').where({
      status: 1,
      is_delete: 0
    }).select();

    const availableTemplates = [];

    for (const template of templates) {
      try {
        // 检查是否已经领取过
        const existingCoupon = await this.model('user_coupon').where({
          user_id: userId,
          coupon_template_id: template.id,
          is_delete: 0
        }).find();

        if (!think.isEmpty(existingCoupon)) {
          template.can_claim = false;
          template.claimed = true;
        } else {
          // 检查剩余数量
          const hasRemaining = await this.model('coupon_template').checkRemainingCount(template.id);
          template.can_claim = hasRemaining;
          template.claimed = false;
        }

        availableTemplates.push(template);
      } catch (error) {
        think.logger.error(`检查优惠券模板 ${template.id} 状态失败:`, error.message);
      }
    }

    return this.success(availableTemplates);
  }
}; 