/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-15 00:05:19
 * @FilePath: /petshop-server/src/api/controller/address.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
const pinyin = require("pinyin");
const generate = require('nanoid/generate');
module.exports = class extends Base {
    async getAddressesAction() {
		const userId = this.getLoginUserId();;
        const addressList = await this.model('address').where({
            user_id: userId,
            is_delete: 0
        }).order('id desc').select();
        let itemKey = 0;
        for (const addressItem of addressList) {
            addressList[itemKey].province_name = await this.model('region').getRegionName(addressItem.province_id);
            addressList[itemKey].city_name = await this.model('region').getRegionName(addressItem.city_id);
            addressList[itemKey].district_name = await this.model('region').getRegionName(addressItem.district_id);
            addressList[itemKey].full_region = addressList[itemKey].province_name + addressList[itemKey].city_name + addressList[itemKey].district_name;
            itemKey += 1;
        }
        return this.success(addressList);
    }
    async saveAddressAction() {
        let addressId = this.post('id');
		const userId = this.getLoginUserId();;
        const addressData = {
            name: this.post('name'),
            mobile: this.post('mobile'),
            province_id: this.post('province_id'),
            city_id: this.post('city_id'),
            district_id: this.post('district_id'),
            address: this.post('address'),
            user_id: this.getLoginUserId(),
            is_default: this.post('is_default')
        };
        if (think.isEmpty(addressId)) {
            addressId = await this.model('address').add(addressData);
        } else {
            await this.model('address').where({
                id: addressId,
                user_id: userId
            }).update(addressData);
        }
        // 如果设置为默认，则取消其它的默认
        if (this.post('is_default') == 1) {
            await this.model('address').where({
                id: ['<>', addressId],
                user_id: userId
            }).update({
                is_default: 0
            });
        }
        const addressInfo = await this.model('address').where({
            id: addressId
        }).find();
        return this.success(addressInfo);
    }
    async deleteAddressAction() {
        const id = this.post('id');
		const userId = this.getLoginUserId();;
        let d = await this.model('address').where({
            user_id: userId,
            id: id
        }).update({
            is_delete: 1
        });
        return this.success(d);
    }
    async addressDetailAction() {
        const addressId = this.get('id');
		const userId = this.getLoginUserId();;
        const addressInfo = await this.model('address').where({
            user_id: userId,
            id: addressId
        }).find();
        if (!think.isEmpty(addressInfo)) {
            addressInfo.province_name = await this.model('region').getRegionName(addressInfo.province_id);
            addressInfo.city_name = await this.model('region').getRegionName(addressInfo.city_id);
            addressInfo.district_name = await this.model('region').getRegionName(addressInfo.district_id);
            addressInfo.full_region = addressInfo.province_name + addressInfo.city_name + addressInfo.district_name;
        }
        return this.success(addressInfo);
    }
};