const _ = require('lodash');
const moment = require('moment');
module.exports = class extends think.Model {
    /**
     * 生成订单的编号order_sn
     * @returns {string}
     */
    generateOrderNumber() {
        const date = new Date();
        return moment().format('YYYYMMDDHHmmss') + _.random(100000, 999999);
    }
    /**
     * 获取订单可操作的选项
     * @param orderId
     */
    async getOrderHandleOption(orderId, orderInfo) {
        const handleOption = {
            cancel: false, // 取消操作
            delete: false, // 删除操作
            pay: false, // 支付操作
            delivery: false, // 确认收货操作
            confirm: false, // 完成订单操作
            buy: false, // 再次购买
            cancel_refund: false, // 是否可以申请退款
            logistics: true, // 物流信息
        };
        if (think.isEmpty(orderInfo)) {
            orderInfo = await this.where({
                id: orderId
            }).find();
        }
        // 订单流程：下单成功－》支付订单－》发货－》收货－》评论
        // 订单相关状态字段设计，采用单个字段表示全部的订单状态
        // 1xx表示订单取消和删除等状态 0订单创建成功等待付款，101订单已取消，102订单已删除
        // 2xx表示订单支付状态,201订单已付款，等待发货
        // 3xx表示订单物流相关状态,300订单已发货，301用户确认收货
        // 4xx表示订单退换货相关的状态,401没有发货，退款402,已收货，退款退货
        // 如果订单已经取消或是已完成，则可删除和再次购买
        //  101：未付款、102：已取消、103已取消(系统)、
        //  201：已付款、202：订单取消，退款中、203：已退款、204：拒绝退款 205：同意退款，等待退货 206：部分退款
        // 300: 备货未发货 301：已发货、302：已收货、303：已收货(系统)、
        // 401：已完成、
        // 801：拼团中,未付款、802：拼团中,已付款',
        if (orderInfo.order_status === 101) {
            handleOption.delete = true;
            handleOption.pay = true;
            handleOption.logistics = false;
        }
        if (orderInfo.order_status === 201) {
            handleOption.buy = true;
            handleOption.cancel_refund = true;
            handleOption.logistics = false;
        }
        if ((orderInfo.order_status > 201 && orderInfo.order_status <= 401)
            && orderInfo.order_status !== 204 &&  orderInfo.order_type !== 203 
            && orderInfo.order_type !== 205 && orderInfo.order_type !== 206) {
            handleOption.buy = true;
            handleOption.cancel_refund = true;
        }
        if (orderInfo.order_status === 205 || orderInfo.order_status === 206) {
            handleOption.cancel_refund = true;
        }
        if (orderInfo.order_status === 301) {
            handleOption.delivery = true;
        }
        if (orderInfo.order_status === 401) {
            handleOption.delete = true;
        }
        if (orderInfo.order_status === 102 || 
            orderInfo.order_status === 103 ) {
            handleOption.delete = true;
            handleOption.buy = true;
            handleOption.logistics = false;
        }
        if (orderInfo.order_status === 203 || 
            orderInfo.order_status === 401) {
            handleOption.delete = true;
            handleOption.buy = true;
        }
        return handleOption;
    }
    //  101：未付款、102：已取消、103已取消(系统)、
    //  201：已付款、202：订单取消，退款中、203：已退款、204：拒绝退款、205：同意退款，等待退货  206：部分退款
    // 300: 备货未发货 301：已发货、302：已收货、303：已收货(系统)、
    // 401：已完成、
    // 801：拼团中,未付款、802：拼团中,已付款',
    async getOrderStatusText(order_status) {
        let statusText = '待付款';
        switch (parseInt(order_status)) {
            case 101:
                statusText = '待付款';
                break;
            case 102:
                statusText = '交易关闭';
                break;
            case 103:
                statusText = '交易关闭'; //到时间系统自动取消
                break;
            case 201:
                statusText = '待发货';
                break;
            case 202:
                statusText = '用户发起退款';
                break;
            case 203:
                statusText = '已退款';
                break;
            case 204:
                statusText = '拒绝退款';
                break;
            case 205:
                statusText = '同意退款，等待退货';
                break;
            case 206:
                statusText = '部分退款';
                break;
            case 300:
                statusText = '待发货';
                break;
            case 301:
                statusText = '已发货';
                break;
            case 302:
                statusText = '已收货';
                break;
            case 303:
                statusText = '已收货'; //到时间系统自动收货、
                break;
            case 401:
                statusText = '交易成功'; //到时间，未收货的系统自动收货、
                break;
        }
        return statusText;
    }
    async getOrderBtnText(orderId) {
        const orderInfo = await this.where({
            id: orderId
        }).find();
        let statusText = '';
        switch (orderInfo.order_status) {
            case 101:
                statusText = '修改价格';
                break;
            case 102:
                statusText = '查看详情';
                break;
            case 103:
                statusText = '查看详情'; //到时间系统自动取消
                break;
            case 201:
                statusText = '备货';
                break;
            case 202:
                statusText = '查看详情';
                break;
            case 203:
                statusText = '查看详情';
                break;
            case 300:
                statusText = '打印快递单';
                break;
            case 301:
                statusText = '查看详情';
                break;
            case 302:
                statusText = '查看详情';
                break;
            case 303:
                statusText = '查看详情'; //到时间，未收货的系统自动收货、
                break;
            case 401:
                statusText = '查看详情'; //到时间，未收货的系统自动收货、
                break;
        }
        if (orderInfo.order_status == 301) {
            statusText = '确认收货'
        }
        return statusText;
    }
};