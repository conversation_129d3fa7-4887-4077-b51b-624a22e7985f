/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-09 23:56:48
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-21 22:45:29
 * @FilePath: /petshop-server/src/admin/model/order_express.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/model/base.js');
const axios = require('axios');
const md5 = require('md5');
const { throwError } = require('../../common/config/utils');
const qs = require('qs'); // 引入qs模块（用来处理 form 格式）
module.exports = class extends Base {
    // ************************* 查询快递100 物流信息********************/
    async queryExpress100(orderId, senderPhone, express_type = 1) {
        const expressInfo = await this.model('order_express').where({
            order_id: orderId,
            express_type: express_type,
            is_delete: 0
        }).find();
        if (think.isEmpty(expressInfo)) {
            throw throwError(-1, "暂无订单物流信息");
        }

        let isFinish = expressInfo.is_finish;

        // 判断当前快递状态是否已经完成，如果未完成，则判断上次查询时间距离当前是否超过三个小时，如果超过，则重新查询
        if (isFinish == 1) {
            return expressInfo;
        }

        let request_time = expressInfo.request_time;
        const shipperCode = expressInfo.shipper_code;
        const logisticCode = expressInfo.logistic_code;
        if (isFinish == 0 && parseInt(new Date().getTime() / 1000) - request_time > 60 * 60 * 3) {
            // 重新查询快递100的接口
            try {
                const queryExpressUrl = think.config('kuaidi100.queryExpressUrl');
                const printKey = think.config('kuaidi100.key');
                const printCustomer = think.config('kuaidi100.customer');
                let paramObj = {
                    com: shipperCode,
                    num: logisticCode,
                    phone: senderPhone,
                    resultv2: '1',  // 添加此字段表示开通行政区域解析功能。0：关闭（默认），1：开通行政区域解析功能，2：开通行政解析功能并且返回出发、目的及当前城市信息
                    show: '0',  // 返回数据格式。0：json（默认），1：xml，2：html，3：text
                    order: 'desc'  // 返回结果排序方式。desc：降序（默认），asc：升序
                };

                // JSON字符串化 param
                const paramStr = JSON.stringify(paramObj);

                // 计算签名（paramStr + key + customer）
                const sign = md5(paramStr + printKey + printCustomer).toUpperCase();

                // 构建最终请求体（注意 param 是字符串）
                const postBody = {
                    customer: printCustomer,
                    param: paramStr,
                    sign: sign
                };

                think.logger.info("请求物流信息快递100入参：", postBody);
                // 发起 POST 请求（表单方式）
                const response = await axios.post(queryExpressUrl, qs.stringify(postBody), {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
                think.logger.info("请求物流信息快递100响应：", response.data);

                // 将物流信息重新插入表中
                // 快递单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等
                const state = response.data.state;
                // 是否签收标记，0未签收，1已签收，
                const ischeck = parseInt(response.data.ischeck || 0);
                await this.model('order_express').where({
                    order_id: orderId,
                    logistic_code: logisticCode
                }).update({
                  traces: JSON.stringify(response.data),
                  is_finish: ischeck,
                  request_time: parseInt(new Date().getTime() / 1000),
                  request_count: expressInfo.request_count + 1
                });

                const newExpressInfo = await this.model('order_express').where({
                    order_id: orderId,
                    logistic_code: logisticCode
                }).find();
                return newExpressInfo;
            } catch (error) {
                think.logger.error("查询物流信息快递100异常：", error);
                throw throwError(-1, "查询物流信息失败");
            }
        }else{
            return expressInfo;
        }
    }
};