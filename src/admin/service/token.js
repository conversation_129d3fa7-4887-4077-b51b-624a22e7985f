/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-29 23:48:47
 * @FilePath: /petshop-server/src/admin/service/token.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const jwt = require('jsonwebtoken');
const secret = 'sdfsdfsdf123123!ASDasdasdasdasda';

const moment = require('moment');
const rp = require('request-promise');
const fs = require('fs');
const http = require("http");

module.exports = class extends think.Service {
    /**
     * 根据header中的x-Auth-token值获取用户id
     */
    getUserId(token) {
        if (!token) {
            return 0;
        }
        const result = this.parse(token);
        if (think.isEmpty(result) || result.user_id <= 0) {
            return 0;
        }
        return result.user_id || result.id;
    }

    getUserType(token) {
        if (!token) {
            return "";
        }
        const result = this.parse(token);
        if (think.isEmpty(result) || !result.userType) {
            return "";
        }
        return result.userType;
    }

    // /**
    //  * 根据值获取用户信息
    //  */
    // async getUserInfo() {
    //     const userId = await this.getUserId();
    //     if (userId <= 0) {
    //         return null;
    //     }

    //     const userInfo = await this.model('admin').where({id: userId}).find();

    //     return think.isEmpty(userInfo) ? null : userInfo;
    // }

    async create(userInfo) {
        const token = jwt.sign(userInfo, secret);
        return token;
    }

    parse(token) {
        if (token) {
            try {
                return jwt.verify(token, secret);
            } catch (err) {
                // if (err.name === 'TokenExpiredError') {
                //     console.log('Token 已过期');
                // } else {
                //     console.log('无效 Token');
                // }
                return null;
            }
        }
        return null;
    }

    async getAccessToken() {
        const options = {
            method: 'POST',
            url: 'https://api.weixin.qq.com/cgi-bin/token',
            qs: {
                grant_type: 'client_credential',
                secret: think.config('weixin.secret'),
                appid: think.config('weixin.appid')
            }
        };
        let sessionData = await rp(options);
        sessionData = JSON.parse(sessionData);
        let token = sessionData.access_token;
        return token;
    }
};
