const Base = require('../../common/controller/base.js');
module.exports = class extends Base {
    async loginAction() {
        const username = this.post('username');
        const password = this.post('password');
        const admin = await this.model('admin').where({
            username: username
        }).find();
        if (think.isEmpty(admin)) {
            return this.fail('用户名或密码不正确!');
        }
        if (admin.is_delete == 1) {
            return this.fail('账号已被禁用!');
        }
        try {
            console.log(think.md5(password + '' + admin.password_salt))
            console.log(admin.password)
        } catch (error) {
            console.log(error)
        }
        if (think.md5(password + '' + admin.password_salt) !== admin.password) {
            return this.fail('用户名或密码不正确!!');
        }
        // 更新登录信息
        await this.model('admin').where({
            id: admin.id
        }).update({
            last_login_time: parseInt(Date.now() / 1000),
            last_login_ip: this.ctx.ip
        });
        const TokenSerivce = this.service('token', 'admin');
        admin.user_id = admin.id
        admin.userType = 'admin';
        const sessionKey = await TokenSerivce.create(JSON.stringify(admin));
        if (think.isEmpty(admin) || think.isEmpty(sessionKey)) {
            return this.fail('登录失败');
        }
        const userInfo = {
            id: admin.id,
            username: admin.username,
            name:admin.name
        };
        return this.success({
            token: sessionKey,
            userInfo: userInfo
        });
    }
};