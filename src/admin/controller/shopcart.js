/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-25 00:40:30
 * @FilePath: /petshop-server/src/admin/controller/shopcart.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
const moment = require('moment');
module.exports = class extends Base {
    /**
     * index action
     * @return {Promise} []
     */
    async indexAction() {
        const page = this.get('page') || 1;
        const size = this.get('size') || 10;
        const name = this.get('name') || '';
        const model = this.model('cart');
        // const data = await model.where({goods_name: ['like', `%${name}%`]}).order(['id DESC']).page(page, size).countSelect();
        // for (const item of data.data) {
        //     item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');
        //     let userInfo = await this.model('user').where({id:item.user_id}).find();
        //     if(think.isEmpty(userInfo)){
        //         item.nickname = '已删除'
        //     }
        // }

        const data = await model.alias('c')
        .field([
            'c.*', 'g.list_pic_url', 'u.nickname'
        ])
        .join([
            {
                table: 'user',
                join: 'left',
                as: 'u',
                on: ['u.id', 'c.user_id']
            },
            {
                table: 'goods',
                join: 'left',
                as: 'g',
                on: ['g.id', 'c.goods_id']
            }
        ]).where({ 'c.goods_name': ['like', `%${name}%`], 'g.is_delete': 0, 'g.is_on_sale': 1, 'u.is_delete': 0 })
        .order(['c.id DESC']).page(page, size).countSelect();

        return this.success(data);
    }

};
