const Base = require('../../common/controller/base.js');

module.exports = class extends Base {
  async indexAction() {
    const model = this.model('shipper');
    const info = await model.where({ enabled: 1 }).select();
    const set = await this.model('settings').find();
    return this.success({ info, set });
  }

  async listAction() {
    const page = this.get('page') || 1;
    const size = this.get('size') || 10;
    const name = this.get('name') || '';
    const model = this.model('shipper');
    const data = await model
      .where({ 'name|code': ['like', `%${name}%`] })
      .order('sort_order ASC')
      .page(page, size)
      .countSelect();
    return this.success(data);
  }

  async enabledStatusAction() {
    const id = this.get('id');
    const status = this.get('status');
    if (!id || status === undefined) return this.fail(400, '缺少参数');

    const sale = status === 'true' ? 1 : 0;
    await this.model('shipper').where({ id }).update({ enabled: sale });
    return this.success();
  }

  async updateSortAction() {
    const id = this.post('id');
    const sort = this.post('sort');
    if (!id || sort === undefined) return this.fail(400, '缺少参数');

    const data = await this.model('shipper').where({ id }).update({ sort_order: sort });
    return this.success(data);
  }

  async infoAction() {
    const id = this.get('id');
    if (!id) return this.fail(400, '参数错误');
    const data = await this.model('shipper').where({ id }).find();
    return this.success(data);
  }

  async storeAction() {
    if (!this.isPost) return false;
    const values = this.post();
    const id = this.post('id');
    const model = this.model('shipper');
    if (id > 0) {
      await model.where({ id }).update(values);
    } else {
      delete values.id;
      await model.add(values);
    }
    return this.success(values);
  }

  /**
   * 删除模版
   * @returns 
   */
  async destoryAction() {
    const id = this.post('id');
    if (!id) return this.fail(400, '缺少 id');
    await this.model('freight_template').where({ id }).limit(1).update({is_delete: 1});
    return this.success();
  }

  /**
   * 模版列表
   * @returns 
   */
  async freightAction() {
    const data = await this.model('freight_template').where({ is_delete: 0 }).select();
    return this.success(data);
  }

  /**
   * 查询地区
   * @returns 
   */
  async getareadataAction() {
    const all = await this.model('region').where({ type: 1 }).field('id,name').select();
    return this.success(all);
  }

  /**
   * 获取模版内容
   * @returns 
   */
  async freightdetailAction() {
    const id = this.post('id');
    if (!id) return this.fail(400, '缺少模板ID');

    const groupModel = this.model('freight_template_group');
	const detailModel = this.model('freight_template_detail');

    const groupData = await groupModel.where({
      template_id: id,
      is_delete: 0
    }).select();

	const group_ids = groupData.map(v => v.id);
	let detailData = [];
	let areaMap = {};
	let areaIdMap = {};
	if (group_ids.length > 0) {
		const regionModel = this.model('region');
		// 根据group_id 分组查询模版详情
		detailData = await detailModel.where({
			template_id: id,
			group_id: ['IN', group_ids],
			is_delete: 0
		}).select();

		// 查询地址信息 相同 group_id 的地址保存
		for (let item of detailData) {
			const areaInfo = await regionModel.where({ id: item.area }).find();
			if (think.isEmpty(areaInfo)) continue;
			item.areaName = areaInfo.name;
			item.areaId = areaInfo.id;
			if (!areaMap[item.group_id]) {
				areaMap[item.group_id] = [];
			}
			if (!areaIdMap[item.group_id]) {
				areaIdMap[item.group_id] = [];
			}
			areaMap[item.group_id].push(areaInfo.name);
			areaIdMap[item.group_id].push(areaInfo.id);
		}
	}

	for (let item of groupData) {
		item.areaName = areaMap[item.id] ? areaMap[item.id].join(',') : '全国';
		item.areaIds = areaIdMap[item.id] ? areaIdMap[item.id].join(',') : '0';
	}

    const freight = await this.model('freight_template').where({ id }).find();
    return this.success({ 
		freight, 
		data: detailData, 
		defaultData: groupData 
	});
  }

  /**
   * 保存模版内容
   * @returns 
   */
  async saveTableAction() {
    const defaultData = this.post('defaultData') || [];
    const info = this.post('info');
    const template_id = info.id;

	await this.transaction('freight_template', async (session) => {
		const model = this.model('freight_template').db(session);
		const groupModel = this.model('freight_template_group').db(session);

		for (const item of defaultData) {
			// 先更新 group
			if (item.id) {
				await groupModel.where({ id: item.id }).update({
					start: item.start,
					start_fee: item.start_fee,
					add: item.add,
					add_fee: item.add_fee,
					free_by_money: item.free_by_money,
					free_by_number: item.free_by_number,
					is_default: item.is_default,
				});
			}else{
				await groupModel.add({
					start: item.start,
					start_fee: item.start_fee,
					add: item.add,
					add_fee: item.add_fee,
					free_by_money: item.free_by_money,
					free_by_number: item.free_by_number,
					template_id,
					is_default: item.is_default,
				});
			}
		}

		await model.where({ id: template_id }).update({
			name: info.name,
			package_price: info.package_price,
			freight_type: info.freight_type,
			freight_use_type: info.freight_use_type
		});
	});

    return this.success();
  }
  /**
   * 新增模版
   * @returns 
   */
  async addTableAction() {
    const info = this.post('info');
    const data = this.post('table') || [];
    const def = this.post('defaultData');

    await this.transaction('freight_template_group', async (session) => {
		const model = this.model('freight_template').db(session);
		const groupModel = this.model('freight_template_group').db(session);
		const detailModel = this.model('freight_template_detail').db(session);

		const template_id = await model.add(info);
		if (def.length > 0) {
			const defVal = {
				start: def[0].start,
				start_fee: def[0].start_fee,
				add: def[0].add,
				add_fee: def[0].add_fee,
				free_by_money: def[0].free_by_money,
				free_by_number: def[0].free_by_number,
				template_id,
			};
			const defGroupId = await groupModel.add(defVal);
			await detailModel.add({ template_id, group_id: defGroupId, area: 0 });
		}

		for (const item of data) {
			const areaStr = item.area.substring(2);
			const areaArr = areaStr.split(',');
			const val = {
				area: areaStr,
				start: item.start,
				start_fee: item.start_fee,
				add: item.add,
				add_fee: item.add_fee,
				template_id,
				free_by_money: item.free_by_money,
				free_by_number: item.free_by_number
			};
			const groupId = await groupModel.add(val);
			for (const code of areaArr) {
				await detailModel.add({ template_id, group_id: groupId, area: code });
			}
		}
  	});


    return this.success();
  }

  /**
   * 保存当前group 的地区信息
   */
  async updateAreaAction() {
	const template_id = this.post('template_id');
	const group_id = this.post('group_id');
	const newAreaList = this.post('newAreaIds');

	if (think.isEmpty(group_id) || think.isEmpty(template_id)) {
		return this.fail(-1, "请先保存模版");
	}

	await this.transaction('freight_template_detail', async (session) => {
		const detailModel = this.model('freight_template_detail').db(session);
		await detailModel.where({
			template_id,
			group_id
		}).update({ is_delete: 1 });

		for (const area of newAreaList) {
			await detailModel.add({
				template_id,
				group_id,
				area
			});
		}
	});

	return this.success();
  }

  /**
   * 删除当前group
   */
  async deleteTemplateGroupAction() {
	const id = this.post('id');
	const template_id = this.post('template_id');
	if (think.isEmpty(id) || think.isEmpty(template_id)) {
		return this.fail(-1, "缺少参数");
	}

	await this.transaction('freight_template_group', async (session) => {
		const groupModel = this.model('freight_template_group').db(session);
		const detailModel = this.model('freight_template_detail').db(session);
		await groupModel.where({ id }).update({ is_delete: 1 });
		await detailModel.where({ template_id, group_id: id }).update({ is_delete: 1 });
	});

	return this.success();
  }


  /****************************** 偏远地区设置 ********************************* */
  async exceptareaAction() {
    const model = this.model('except_area');
    const data = await model.where({ is_delete: 0 }).select();
    for (const item of data) {
      const areaArr = item.area.split(',');
      const info = await this.model('region').where({ id: ['IN', areaArr] }).getField('name');
      item.areaName = info.join(',');
    }
    return this.success(data);
  }

  async exceptAreaDeleteAction() {
    const id = this.post('id');
    if (!id) return this.fail(400, '缺少 id');
    await this.model('except_area').where({ id }).update({ is_delete: 1 });
    await this.model('except_area_detail').where({ except_area_id: id }).update({ is_delete: 1 });
    return this.success();
  }

  async exceptAreaDetailAction() {
    const id = this.post('id');
    const data = await this.model('except_area').where({ id, is_delete: 0 }).find();
    if (think.isEmpty(data)) return this.fail(404, '数据不存在');
    const areaArr = data.area.split(',');
    const info = await this.model('region').where({ id: ['IN', areaArr] }).getField('name');
    data.areaName = info.join(',');
    return this.success(data);
  }

  async saveExceptAreaAction() {
    const table = this.post('table');
    const info = this.post('info');
    const areaStr = table[0].area;
    const areaArr = areaStr.split(',');

    await this.model('except_area').where({ id: info.id }).update({
      area: areaStr,
      content: info.content
    });

    await this.model('except_area_detail').where({
      area: ['NOTIN', areaArr],
      except_area_id: info.id,
      is_delete: 0
    }).update({ is_delete: 1 });

    for (const area of areaArr) {
      const exist = await this.model('except_area_detail').where({
        except_area_id: info.id,
        area,
        is_delete: 0
      }).find();
      if (think.isEmpty(exist)) {
        await this.model('except_area_detail').add({ except_area_id: info.id, area });
      }
    }

    return this.success();
  }

  async addExceptAreaAction() {
    const table = this.post('table');
    const info = this.post('info');
    const areaStr = table[0].area.substring(2);
    const areaArr = areaStr.split(',');

    const id = await this.model('except_area').add({
      area: areaStr,
      content: info.content
    });

    for (const area of areaArr) {
      await this.model('except_area_detail').add({
        except_area_id: id,
        area
      });
    }

    return this.success();
  }
};