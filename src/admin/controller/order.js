const Base = require('../../common/controller/base.js');
const moment = require('moment');
const _ = require('lodash');
const md5 = require('md5');
const axios = require('axios');
const rp = require('request-promise');
const isProd = think.env === 'production';
module.exports = class extends Base {
    /**
     * index action
     *  //  101：未付款、102：已取消、103已取消(系统)、
        //  201：已付款、202：订单取消，退款中、203：已退款、204：拒绝退款 205：同意退款，等待退货， 206：部分退款
        // 300: 备货未发货 301：已发货、302：已收货、303：已收货(系统)、
        // 401：已完成、
        // 801：拼团中,未付款、802：拼团中,已付款',
     * @return {Promise} []
     */
    
    // 查询最近48小时时间段内不同订单状态个数
    async recentOrderCountAction() {
        const startTime = moment().subtract(48, 'hours').format('YYYY-MM-DD HH:mm:ss');
        const endTime = moment().format('YYYY-MM-DD HH:mm:ss');

        const where = {
            is_delete: 0,
            order_type: ['<', 7],
            create_time: ['BETWEEN', [startTime, endTime]],
            order_status: ['IN', [201, 202, 300]]
        };

        const raw = await this.model('order')
            .where(where)
            .field('order_status, COUNT(*) as total')
            .group('order_status')
            .select();

        // 聚合为两类：待发货（201, 300），申请售后（202）
        let pendingDelivery = 0;
        let afterSales = 0;

        for (const row of raw) {
            const status = Number(row.order_status);
            const count = Number(row.total);

            if ([201, 300].includes(status)) {
                pendingDelivery += count;
            } else if (status === 202) {
                afterSales += count;
            }
        }

        return this.success([
            { status_group: [201, 300], status_text: '待发货', total: pendingDelivery },
            { status_group: [202], status_text: '申请售后', total: afterSales }
        ]);
    }
    async indexAction() {
        const page = Number(this.get('page')) || 1;
        const size = Number(this.get('size')) || 10;
        const orderSn = this.get('orderSn') || '';
        const consignee = this.get('consignee') || '';
        const logistic_code = this.get('logistic_code') || '';
        const statusLabel = this.get('status') || ''; // 使用语义化状态

        const keywords = this.get('keywords') || ''; 

        const startTime = this.get('startTime') || '';
        const endTime = this.get('endTime') || '';
        
        const orderModel = this.model('order');
        const userModel = this.model('user');
        const orderGoodsModel = this.model('order_goods');
        const expressModel = this.model('order_express');
        const regionModel = this.model('region');
        
        // 状态映射
        const statusMap = {
            '201,300': [201,300],
            '202': [202, 205],
            '101': [101],
            '102,103': [102, 103],
            '202,205': [202, 205],
            '203': [203],
            '204': [204],
            '301,302,303': [301, 302, 303],
            '401': [401],
            '': [] // 空数组表示不加状态条件
        };
        
        let orderList = [];
        
        // 1. 快递单号直接查
        if (logistic_code) {
            const orderData = await expressModel.where({ logistic_code }).find();
            if (think.isEmpty(orderData)) {
                return this.success({ data: [], count: 0 });
            }
            const order_id = orderData.order_id;
            orderList = await orderModel.alias('o')
                .field('o.*, ct.name as coupon_name')
                .join({
                    table: 'coupon_template',
                    join: 'left',
                    as: 'ct',
                    on: ['ct.id', 'o.coupon_template_id']
                }).where({ 'o.id': order_id }).order(['o.id DESC']).page(page, size).countSelect();
        } else {
            // 2. 通用查询
            const whereMap = {
                'o.order_sn': ['like', `%${orderSn}%`],
                'o.consignee': ['like', `%${consignee}%`],
                'o.order_type': ['<', 7]
            };

            let complex = {
                _logic: 'or',
                'o.order_sn': ['like', `%${keywords}%`],
                'o.consignee': ['like', `%${keywords}%`],
                'o.print_info': ['like', `%${keywords}%`]
            }

            if (startTime && endTime) {
                whereMap['o.create_time'] = ['BETWEEN', [startTime, endTime]];
            }
        
            if (statusLabel && statusMap[statusLabel]) {
                const statusArray = statusMap[statusLabel];
                if (statusArray.length) {
                    whereMap['o.order_status'] = ['IN', statusArray];
                }
            }
        
            orderList = await orderModel.alias('o')
                .field('o.*, ct.name as coupon_name')
                .join({
                    table: 'coupon_template',
                    join: 'left',
                    as: 'ct',
                    on: ['ct.id', 'o.coupon_template_id']
                }).where({
                    _complex: complex,
                    ...whereMap
                }).order(['o.id DESC']).page(page, size).countSelect();
        }
        
        const orders = orderList.data;
        if (orders.length === 0) {
            return this.success({ data: [], count: orderList.count });
        }
        
        const orderIds = orders.map(o => o.id);
        const userIds = [...new Set(orders.map(o => o.user_id))];
        const regionIds = [...new Set([
            ...orders.map(o => o.province),
            ...orders.map(o => o.city),
            ...orders.map(o => o.district)
        ])];
        
        // 批量查相关数据
        const [goodsList, users, regions, expressList] = await Promise.all([
            orderGoodsModel
            .field('order_id,goods_name,goods_aka,list_pic_url,number,goods_specifition_name,retail_price')
            .where({ order_id: ['IN', orderIds], is_delete: 0 }).select(),
        
            userIds.length ? userModel
            .field('id,nickname,name,mobile,avatar')
            .where({ id: ['IN', userIds] }).select() : [],
        
            regionIds.length ? regionModel
            .field('id,name')
            .where({ id: ['IN', regionIds] }).select() : [],
        
            expressModel.where({ order_id: ['IN', orderIds] }).select()
        ]);
        
        const goodsMap = _.groupBy(goodsList, 'order_id');
        const userMap = _.keyBy(users, 'id');
        const regionMap = _.keyBy(regions, 'id');
        const expressMap = _.keyBy(expressList, 'order_id');
        
        for (const order of orders) {
            // 商品信息
            order.goodsList = goodsMap[order.id] || [];
            order.goodsCount = order.goodsList.reduce((sum, item) => sum + item.number, 0);
        
            // 用户信息
            const user = userMap[order.user_id] || { nickname: '已删除', name: '', mobile: '', avatar: '' };
            order.userInfo = user;
        
            // 地区
            const province = regionMap[order.province].name || '';
            const city = regionMap[order.city].name || '';
            const district = regionMap[order.district].name || '';
            order.full_region = province + city + district;
        
            // 时间格式化
            order.add_time = moment(order.create_time).format('YYYY-MM-DD HH:mm:ss');
            order.pay_time = order.pay_time ? moment.unix(order.pay_time).format('YYYY-MM-DD HH:mm:ss') : '';
            order.shipping_time = order.shipping_time ? moment.unix(order.shipping_time).format('YYYY-MM-DD HH:mm:ss') : '';
            order.confirm_time = order.confirm_time ? moment.unix(order.confirm_time).format('YYYY-MM-DD HH:mm:ss') : '';
            order.refund_time = order.refund_time ? moment.unix(order.refund_time).format('YYYY-MM-DD HH:mm:ss') : '';
        
            // 状态文本
            order.order_status_text = await orderModel.getOrderStatusText(order.order_status);
        
            // 快递信息
            const express = expressMap[order.id];
            order.expressInfo = express ? `${express.shipper_name} ${express.logistic_code}` : '';
        }
        
        return this.success({
            data: orders,
            count: orderList.count,
            currentPage: page
        });
    }
    async getAutoStatusAction() {
        let status = await this.model('settings').where({
            id: 1
        }).field('auto_delivery').find();
        let info = status.auto_delivery;
        return this.success(info);
    }
    async toDeliveryAction() {
        const page = this.get('page') || 1;
        const size = this.get('size') || 10;
        const status = this.get('status') || '';
        const model = this.model('order');
        const data = await model.where({
            order_status: status,
        }).order(['id DESC']).page(page, size).countSelect();
        for (const item of data.data) {
            item.goodsList = await this.model('order_goods').field('goods_name,list_pic_url,number,goods_specifition_name,retail_price').where({
                order_id: item.id
            }).select();
            item.goodsCount = 0;
            item.goodsList.forEach(v => {
                item.goodsCount += v.number;
            });
            let province_name = await this.model('region').where({
                id: item.province
            }).getField('name', true);
            let city_name = await this.model('region').where({
                id: item.city
            }).getField('name', true);
            let district_name = await this.model('region').where({
                id: item.district
            }).getField('name', true);
            item.address = province_name + city_name + district_name + item.address;
            item.add_time = moment(item.create_time).format('YYYY-MM-DD HH:mm:ss');
            item.order_status_text = await this.model('order').getOrderStatusText(item.order_status);
            item.button_text = await this.model('order').getOrderBtnText(item.id);
        }
        return this.success(data);
    }
    async saveGoodsListAction() {
        // console.log(typeof(data));
        let id = this.post('id');
        let order_id = this.post('order_id');
        let number = this.post('number');
        let price = this.post('retail_price');
        let addOrMinus = this.post('addOrMinus');
        let changePrice = Number(number) * Number(price);
        console.log(order_id);
        console.log(changePrice);
        if (addOrMinus == 0) {
            await this.model('order_goods').where({
                id: id
            }).decrement('number', number);
            await this.model('order').where({
                id: order_id
            }).decrement({
                actual_price: changePrice,
                order_price: changePrice,
                goods_price: changePrice
            });
            let order_sn = this.model('order').generateOrderNumber();
            await this.model('order').where({
                id: order_id
            }).update({
                order_sn: order_sn
            });
            return this.success(order_sn);
        } else if (addOrMinus == 1) {
            await this.model('order_goods').where({
                id: id
            }).increment('number', number);
            await this.model('order').where({
                id: order_id
            }).increment({
                actual_price: changePrice,
                order_price: changePrice,
                goods_price: changePrice
            });
            let order_sn = this.model('order').generateOrderNumber();
            await this.model('order').where({
                id: order_id
            }).update({
                order_sn: order_sn
            });
            return this.success(order_sn);
        }
    }
    async goodsListDeleteAction() {
        console.log(this.post('id'));
        let id = this.post('id');
        let order_id = this.post('order_id');
        let number = this.post('number');
        let price = this.post('retail_price');
        let addOrMinus = this.post('addOrMinus');
        let changePrice = Number(number) * Number(price);
        console.log(order_id);
        console.log(changePrice);
        await this.model('order_goods').where({
            id: id
        }).update({
            is_delete: 1
        });
        await this.model('order').where({
            id: order_id
        }).decrement({
            actual_price: changePrice,
            order_price: changePrice,
            goods_price: changePrice
        });
        let order_sn = this.model('order').generateOrderNumber();
        await this.model('order').where({
            id: order_id
        }).update({
            order_sn: order_sn
        });
        return this.success(order_sn);
    }
    async saveAdminMemoAction() {
        const id = this.post('id');
        const text = this.post('text');
        const model = this.model('order');
        let info = {
            admin_memo: text
        }
        let data = await model.where({
            id: id
        }).update(info);
        return this.success(data);
    }
    async savePrintInfoAction() {
        const id = this.post('id');
        const print_info = this.post('print_info');
        const model = this.model('order');
        let info = {
            print_info: print_info
        };
        let data = await model.where({
            id: id
        }).update(info);
        return this.success(data);
    }
    async saveExpressValueInfoAction() {
        const id = this.post('id');
        const express_value = this.post('express_value');
        const model = this.model('order');
        let info = {
            express_value: express_value
        };
        let data = await model.where({
            id: id
        }).update(info);
        return this.success(data);
    }
    async saveRemarkInfoAction() {
        const id = this.post('id');
        const remark = this.post('remark');
        const model = this.model('order');
        let info = {
            remark: remark
        };
        let data = await model.where({
            id: id
        }).update(info);
        return this.success(data);
    }
    
    async getAllRegionAction() { // 我写的算法
        const model = this.model('region');
        const aData = await model.where({
            type: 1
        }).select();
        const bData = await model.where({
            type: 2
        }).select();
        const cData = await model.where({
            type: 3
        }).select();
        let newData = [];
        for (const item of aData) {
            let children = [];
            for (const bitem of bData) {
                let innerChildren = [];
                for (const citem of cData) {
                    if (citem.parent_id == bitem.id) {
                        innerChildren.push({
                            value: citem.id,
                            label: citem.name
                        })
                    }
                }
                if (bitem.parent_id == item.id) {
                    children.push({
                        value: bitem.id,
                        label: bitem.name,
                        children: innerChildren
                    })
                }
            }
            newData.push({
                value: item.id,
                label: item.name,
                children: children
            });
        }
        return this.success(newData);
    }
    async orderpackAction() {
        const id = this.get('orderId');
        const model = this.model('order');
        const data = await model.where({
            id: id
        }).update({
            order_status: 300
        });
    }
    async orderReceiveAction() {
        const id = this.get('orderId');
        let currentTime = parseInt(new Date().getTime() / 1000);
        const model = this.model('order');
        const data = await model.where({
            id: id
        }).update({
            order_status: 302,
            shipping_time: currentTime
        });
    }
    async orderPriceAction() {
        const id = this.post('orderId');
        const goodsPrice = this.post('goodsPrice');
        const freightPrice = this.post('freightPrice');
        const actualPrice = this.post('actualPrice');
        const model = this.model('order');
        const data = await model.where({
            id: id
        }).find();
        if (data.order_status != 101 && data.order_status != 801) {
            // 只有待付款和拼团待付款状态才可以修改
            return this.fail('当前订单已不允许修改金额');
        }
        let newData = {
            actual_price: actualPrice,
            freight_price: freightPrice,
            goods_price: goodsPrice,
            order_sn: model.generateOrderNumber()
        }
        await model.where({
            id: id
        }).update(newData);

        return this.success(newData);
    }

    async orderExpressAdd(ele, orderId) {
        let currentTime = parseInt(new Date().getTime() / 1000);
        let info = await this.model('order_express').where({
            order_id: orderId
        }).find();
        if (think.isEmpty(info)) {
            let orderInfo = ele.Order;
            let ShipperCode = orderInfo.ShipperCode;
            let logistic_code = orderInfo.LogisticCode;
            
            const model = this.model('order');
            let kdInfo = await this.model('shipper').where({
                code: ShipperCode
            }).find();
            let kdData = {
                order_id: orderId,
                shipper_name: kdInfo.name,
                shipper_code: ShipperCode,
                logistic_code: logistic_code,
                traces: ""
            };
            await this.model('order_express').add(kdData);
        } else {
            let orderInfo = ele.Order;
            await this.model('order_express').where({
                order_id: orderId
            }).update({
                logistic_code: orderInfo.LogisticCode
            });
        }
        // 如果生成快递单号了。然后又最后没有使用，又去生成快递单号，那么应该重新生成下订单号，用新订单号去生成快递单号，然后update掉旧的order_express
    }
    
    async goPrintOnlyAction() {
        let orderId = this.post('order_id');
        let updateData = {
            print_status: 1
        };
        let data = await this.model('order').where({
            id: orderId
        }).update(updateData);
        return this.success(data);
    }
    async orderDeliveryAction() { // 发货api
        const orderId = this.get('orderId');
        const method = this.get('method');
        const deliveryId = this.get('shipper') || 0;
        const logistic_code = this.get('logistic_code') || 0;
        const model = this.model('order');
        let currentTime = parseInt(new Date().getTime() / 1000);
        let expressName = '';
        if (method == 2) {
            let ele = await this.model('order_express').where({
                order_id: orderId
            }).find();
            if (think.isEmpty(ele)) {
                let kdInfo = await this.model('shipper').where({
                    id: deliveryId
                }).find();
                expressName = kdInfo.name;
                let kdData = {
                    order_id: orderId,
                    shipper_name: kdInfo.name,
                    shipper_code: kdInfo.code,
                    logistic_code: logistic_code,
                    add_time: currentTime,
                    traces: ""
                };
                await this.model('order_express').add(kdData);
                let updateData = {
                    order_status: 301,
                    shipping_time: currentTime
                };
                await this.model('order').where({
                    id: orderId
                }).update(updateData);
                // 发送服务消息
            } else {
                let kdInfo = await this.model('shipper').where({
                    id: deliveryId
                }).find();
                expressName = kdInfo.name;
                let kdData = {
                    order_id: orderId,
                    shipper_name: kdInfo.name,
                    shipper_code: kdInfo.code,
                    logistic_code: logistic_code,
                    add_time: currentTime
                }
                await this.model('order_express').where({
                    order_id: orderId
                }).update(kdData);
            }
        } else if (method == 3) {
            let updateData = {
                order_status: 301,
                shipping_time: currentTime
            };
            await this.model('order').where({
                id: orderId
            }).update(updateData);
            expressName = '自提件';
        }
		await this.deliveryMessage(method,orderId,expressName,logistic_code);
    }
	async deliveryMessage(method,orderId,expressName,logistic_code){
		let orderInfo = await this.model('order').where({
		    id: orderId
		}).field('user_id').find();
		let user = await this.model('user').where({
		    id: orderInfo.user_id
		}).field('weixin_openid').find();
		let openId = user.weixin_openid;
		// 物品名称
		// 快递单号
		// 快递公司
		// 发货时间
		// 温馨提示
		let goodsInfo = await this.model('order_goods').where({
		    order_id: orderId
		}).field('goods_name').select();
		// 物品名称
		let goodsName = '';
		if (goodsInfo.length == 1) {
		    goodsName = goodsInfo[0].goods_name
		} else {
		    goodsName = goodsInfo[0].goods_name + '等' + goodsInfo.length + '件商品'
		}
		// 支付时间
		let currentTime = parseInt(new Date().getTime() / 1000);
		let shippingTime = moment.unix(currentTime).format('YYYY-MM-DD HH:mm:ss');
		// 订单金额
		// 订阅消息 请先在微信小程序的官方后台设置好订阅消息模板，然后根据自己的data的字段信息，设置好data
		let TEMPLATE_ID = think.config('templateId.deliveryId');
		let message = {
		    "touser": openId,
		    "template_id": TEMPLATE_ID,
		    "page": '/pages/ucenter/index/index',
		    "miniprogram_state":"formal",
		    "lang":"zh_CN",
		    "data": {
		      "thing7": {
		          "value": goodsName
		      },
		      "date2": {
		          "value": shippingTime
		      },
		      "name3": {
		          "value": expressName
		      },
		      "character_string4": {
		          "value": logistic_code
		      } ,
		      "thing9": {
		          "value": '签收前请检查包裹！'
		      }
		  }
		}
		const tokenServer = think.service('weixin', 'api');
		const token = await tokenServer.getAccessToken();
		const res = await tokenServer.sendMessage(token,message);
	}

    async saveAddressAction() {
        const sn = this.post('order_sn');
        const name = this.post('name');
        const mobile = this.post('mobile');
        const cAddress = this.post('cAddress');
        const addOptions = this.post('addOptions');
        const province = addOptions[0];
        const city = addOptions[1];
        const district = addOptions[2];
        let info = {
            consignee: name,
            mobile: mobile,
            address: cAddress,
            province: province,
            city: city,
            district: district
        }
        const model = this.model('order');
        const data = await model.where({
            order_sn: sn
        }).update(info);
        return this.success(data);
    }
    async storeAction() {
        if (!this.isPost) {
            return false;
        }
        const values = this.post();
        const id = this.post('id');
        const model = this.model('order');
        values.is_show = values.is_show ? 1 : 0;
        values.is_new = values.is_new ? 1 : 0;
        if (id > 0) {
            await model.where({
                id: id
            }).update(values);
        } else {
            delete values.id;
            await model.add(values);
        }
        return this.success(values);
    }
    async changeStatusAction() {
        const orderSn = this.post('orderSn');
        const value = this.post('status');
        const info = await this.model('order').where({
            order_sn: orderSn
        }).update({
            order_status: value
        });
        return this.success(info);
    }
    async destoryAction() {
        const id = this.post('id');
        await this.model('order').where({
            id: id
        }).limit(1).update({
            is_delete: 1
        });
        // 删除订单商品
        await this.model('order_goods').where({
            order_id: id
        }).update({
            is_delete: 1
        });
        // TODO 事务，验证订单是否可删除（只有失效的订单才可以删除）
        return this.success();
    }
    async getGoodsSpecificationAction() {
        const goods_id = this.post('goods_id');
        let data = await this.model('goods_specification').where({
            goods_id: goods_id,
            is_delete: 0
        }).field('id,value').select();
        return this.success(data);
    }

    // ***************** 新订单逻辑 ****************//
    /**
     * 订单详情
     */
    async detailAction() {
        const orderId = this.get('orderId');
        const orderModel = this.model('order');
      
        // 获取订单基本信息
        const order = await orderModel.alias('o')
            .field('o.*, ct.name as coupon_name')
            .join({
                table: 'coupon_template',
                join: 'left',
                as: 'ct',
                on: ['ct.id', 'o.coupon_template_id']
            }).where({ 'o.id': orderId }).find();
        if (think.isEmpty(order)) return this.fail(404, '订单不存在');
      
        // 合并查询商品和对应的 product.goods_sn
        const goodsList = await this.model('order_goods')
          .alias('og')
          .field([
            'og.id', 'og.product_id', 'og.goods_name', 'og.goods_aka', 'og.list_pic_url',
            'og.number', 'og.goods_specifition_name', 'og.retail_price', 'og.goods_id',
            'p.goods_sn'
          ])
          .join({
            table: 'product',
            join: 'left',
            as: 'p',
            on: ['og.product_id', 'p.id']
          })
          .where({ 'og.order_id': order.id, 'og.is_delete': 0 })
          .select();
      
        // 计算商品总数
        const goodsCount = goodsList.reduce((sum, item) => sum + item.number, 0);
      
        // 并发获取其他信息
        const [
          userInfo,
          regionList,
          orderStatusText,
          settings
        ] = await Promise.all([
          this.model('user').where({ id: order.user_id }).find(),
          this.model('region').where({ id: ['IN', [order.province, order.city, order.district]] }).select(),
          orderModel.getOrderStatusText(order.order_status),
          this.model('settings').find()
        ]);
      
        // 组装区域名称
        const regionMap = regionList.reduce((map, item) => {
          map[item.id] = item.name;
          return map;
        }, {});
        const provinceName = regionMap[order.province] || '';
        const cityName = regionMap[order.city] || '';
        const districtName = regionMap[order.district] || '';
        const fullRegion = `${provinceName}${cityName}${districtName}`;
      
        // 格式化时间字段
        const formatTime = ts => ts && ts !== 0 ? moment.unix(ts).format('YYYY-MM-DD HH:mm:ss') : '';
        order.add_time = moment(order.create_time).format('YYYY-MM-DD HH:mm:ss');
        order.pay_time = formatTime(order.pay_time);
        order.shipping_time = formatTime(order.shipping_time);
        order.confirm_time = formatTime(order.confirm_time);
        order.dealdone_time = formatTime(order.dealdone_time);
      
        // 构造返回数据结构
        const orderInfo = {
          ...order,
          goodsList,
          goodsCount,
          user_name: userInfo.nickname,
          avatar: userInfo.avatar,
          order_status_text: orderStatusText,
          full_region: fullRegion,
          allAddress: fullRegion + order.address
        };
      
        const receiver = {
          name: order.consignee,
          mobile: order.mobile,
          province: provinceName,
          province_id: order.province,
          city: cityName,
          city_id: order.city,
          district: districtName,
          district_id: order.district,
          address: order.address
        };
      
        const sender = {
          name: settings.name,
          mobile: settings.tel,
          province: settings.province_name,
          city: settings.city_name,
          district: settings.district_name,
          province_id: settings.province_id,
          city_id: settings.city_id,
          district_id: settings.district_id,
          address: settings.address
        };

        // 查询订单物流模版信息
        if (orderInfo.freight_template_id == 0) {
            orderInfo.freightTemplateTitle = '默认物流';
        }else{
            const freightTemplate = await this.model('freight_template').where({ id: orderInfo.freight_template_id }).find();
            orderInfo.freightTemplateTitle = freightTemplate.name;
        }
      
        return this.success({
          orderInfo,
          receiver,
          sender
        });
    }

    /**
     * 订单详情
     */
    async detailBatchAction() {
        const orderIds = this.post('orderIds') || []; // 支持 POST 传参，或你也可以用 this.get()
        if (!Array.isArray(orderIds) || orderIds.length === 0) {
            return this.fail(400, '请传入有效的 orderIds 数组');
        }

        const orderModel = this.model('order');

        // 获取所有订单基本信息
        const orders = await orderModel.alias('o')
            .field('o.*, ct.name as coupon_name')
            .join({
                table: 'coupon_template',
                join: 'left',
                as: 'ct',
                on: ['ct.id', 'o.coupon_template_id']
            })
            .where({ 'o.id': ['IN', orderIds] })
            .select();

        if (think.isEmpty(orders)) return this.fail(404, '订单不存在');

        // 获取所有订单 ID
        const allOrderIds = orders.map(o => o.id);

        // 获取所有商品
        const goodsList = await this.model('order_goods')
            .alias('og')
            .field([
                'og.id', 'og.product_id', 'og.goods_name', 'og.goods_aka', 'og.list_pic_url',
                'og.number', 'og.goods_specifition_name', 'og.retail_price', 'og.goods_id',
                'og.order_id',
                'p.goods_sn'
            ])
            .join({
                table: 'product',
                join: 'left',
                as: 'p',
                on: ['og.product_id', 'p.id']
            })
            .where({ 'og.order_id': ['IN', allOrderIds], 'og.is_delete': 0 })
            .select();

        // 获取所有用户 ID
        const userIds = [...new Set(orders.map(o => o.user_id))];
        const users = await this.model('user').where({ id: ['IN', userIds] }).select();
        const userMap = users.reduce((map, u) => {
            map[u.id] = u;
            return map;
        }, {});

        // 获取所有地区 ID
        const regionIds = [
            ...new Set(orders.flatMap(o => [o.province, o.city, o.district]))
        ];
        const regionList = await this.model('region').where({ id: ['IN', regionIds] }).select();
        const regionMap = regionList.reduce((map, item) => {
            map[item.id] = item.name;
            return map;
        }, {});

        // 获取订单状态文本
        const orderStatusTexts = await Promise.all(
            orders.map(o => orderModel.getOrderStatusText(o.order_status))
        );

        // 获取设置
        const settings = await this.model('settings').find();

        // 格式化时间方法
        const formatTime = ts => ts && ts !== 0 ? moment.unix(ts).format('YYYY-MM-DD HH:mm:ss') : '';

        // 构造返回结果
        const result = orders.map((order, index) => {
            const userInfo = userMap[order.user_id] || {};
            const goods = goodsList.filter(g => g.order_id === order.id);
            const goodsCount = goods.reduce((sum, item) => sum + item.number, 0);

            const provinceName = regionMap[order.province] || '';
            const cityName = regionMap[order.city] || '';
            const districtName = regionMap[order.district] || '';
            const fullRegion = `${provinceName}${cityName}${districtName}`;

            const orderInfo = {
                ...order,
                goodsList: goods,
                goodsCount,
                user_name: userInfo.nickname,
                avatar: userInfo.avatar,
                order_status_text: orderStatusTexts[index],
                full_region: fullRegion,
                allAddress: fullRegion + order.address,
                add_time: moment(order.create_time).format('YYYY-MM-DD HH:mm:ss'),
                pay_time: formatTime(order.pay_time),
                shipping_time: formatTime(order.shipping_time),
                confirm_time: formatTime(order.confirm_time),
                dealdone_time: formatTime(order.dealdone_time)
            };

            const receiver = {
                name: order.consignee,
                mobile: order.mobile,
                province: provinceName,
                province_id: order.province,
                city: cityName,
                city_id: order.city,
                district: districtName,
                district_id: order.district,
                address: order.address
            };

            const sender = {
                name: settings.name,
                mobile: settings.tel,
                province: settings.province_name,
                city: settings.city_name,
                district: settings.district_name,
                province_id: settings.province_id,
                city_id: settings.city_id,
                district_id: settings.district_id,
                address: settings.address
            };

            return {
                orderInfo,
                receiver,
                sender
            };
        });

        return this.success(result);
    }

    // 手动发货
    async goDeliveryAction() {
        const orderId = this.post('order_id');
        const logistic_code = this.post('logistic_code');
        const mobile = this.post('mobile');
        const shipper_code = this.post('shipper_code');

        if (!orderId || !logistic_code || !mobile ||!shipper_code) {
            throw this.throwError(-1, '参数错误');
        }
        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        if (think.isEmpty(orderInfo)) {
            throw this.throwError(-1, '订单信息错误');
        }
        if (!(orderInfo.order_status == 201 || orderInfo.order_status == 300)) {
            throw this.throwError(-1, '订单状态错误');
        }

        // 查询当前订单的用户
        const userInfo = await this.model('user').where({
            id: orderInfo.user_id
        }).find();

        // 根据nowDeliveryId 查出物流公司编码和名称
        const deliveryInfo = await this.model('shipper').where({
            code: shipper_code
        }).find();
        await this.transaction('order', async (session) => { 
            // 创建订单快递单成功后，自动发货
            let updateData = {
                order_status: 301,
                mobile: mobile,
                shipping_time: parseInt(new Date().getTime() / 1000)
            };
            await this.model('order').db(session).where({
                id: orderId
            }).update(updateData);

            let kdData = {
                order_id: orderId,
                logistic_code: logistic_code,
                shipper_name: deliveryInfo.name,
                shipper_code: deliveryInfo.code,
                delivery_type: 2,
                traces: ''
            }
            await this.model('order_express').db(session).where({
                order_id: orderId,
            }).add(kdData);

            // 发货成功发送短信通知
            const orderService = think.service('order', 'admin');
            const smsError = await orderService.sendSMS(
                "2473730",
                [orderInfo.order_sn, logistic_code],
                [`+86${userInfo.mobile || orderInfo.mobile}`],
            );
            if (smsError) {
                think.logger.error("订单发货成功发送短信通知失败：", smsError);
            }
        });

        return this.success();
    }

    // ***************** 快递100 打印 ****************//
    /**
     * 打印面单一定会在订单物流表中插入一条数据，所以订单状态一点会变成已发货
     * @returns 
     */
    async createdOrderExpressAction() {
        const printInfo = this.post('printInfo');
        const sender = this.post('sender');
        const receiver = this.post('receiver');
        const expressType = this.post('expressType');
        const expressCode = this.post('expressCode');
        const partnerId = this.post('partnerId');
        const orderSn = this.post('orderSn');

        if (think.isEmpty(printInfo) || 
        think.isEmpty(sender) || 
        think.isEmpty(receiver) || 
        think.isEmpty(expressType) || 
        think.isEmpty(expressCode) ||
        think.isEmpty(partnerId) ||
        think.isEmpty(orderSn)
        ) {
            return this.fail('参数错误');
        }

        let orderInfo = await this.model('order').where({
            order_sn: orderSn
        }).find();

        if (think.isEmpty(orderInfo)) {
            return this.fail('订单信息错误');
        }

        if (!(orderInfo.order_status == 201 || orderInfo.order_status == 300)) {
            return this.fail('订单状态错误');
        }

        let shipperInfo = await this.model("shipper").where({
            code: expressCode,
            temp_no: expressType,
            enabled: 1
        }).find();

        if (think.isEmpty(shipperInfo)) {
            return this.fail('快递公司信息错误');
        }

        const printOrderUrl = think.config('kuaidi100.printOrderUrl');
        const printKey = think.config('kuaidi100.key');
        const printSecret = think.config('kuaidi100.secret');
        const siid = think.config('kuaidi100.siid');
        let param = {
            printType: "CLOUD",
            siid: siid,
            partnerId: partnerId,
            kuaidicom: expressCode,
            tempId: expressType,
            count: 1,
            reorder: true,
            orderId: orderInfo.order_sn,
            net: shipperInfo.net,
            code: shipperInfo.partner_code,
            partnerKey: shipperInfo.partner_key,
            cargo: printInfo.printCargo,
            remark: printInfo.printInfo,
            recMan: {
                // 收件人信息
                name: receiver.name,
                mobile: receiver.mobile,
                printAddr: receiver.province + receiver.city + receiver.district + receiver.address,
            },
            sendMan: {
                // 发件人信息
                name: sender.name,
                mobile: sender.mobile,
                printAddr: sender.province + sender.city + sender.district + sender.address,
            },
            customParam: {
                orderInfo: printInfo.printInfo
            }
        };

        // 当前时间戳
        const t = new  Date().valueOf();
        // 请求签名 32位大写，签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号，
        const sign = md5(JSON.stringify(param) + t + printKey + printSecret).toUpperCase();

        const params = {
            key: printKey,
            sign: sign,
            method: 'order', // 注意要加上
            t: t,
            param: JSON.stringify(param) // 这里同样要 stringify
        };
        think.logger.info("请求打印面单快递100入参：", params);
        try {
            if (isProd) {
                const response = await axios.get(printOrderUrl, {
                    headers: {
                        'content-type': 'application/x-www-form-urlencoded'
                    },  
                    params 
                });
                think.logger.info("请求打印面单快递100响应：", response.data);
                if (!(response.data.code == 200 || response.data.code == 30011)) {
                    return this.fail(response.data.message);
                }

                // 订单发货
                await this.orderSendAction(
                    orderInfo, shipperInfo, response.data.data.kuaidinum,
                    response.data.data.taskId
                );

                // 发货成功发送短信通知
                // 查询当前订单的用户
                const userInfo = await this.model('user').where({
                    id: orderInfo.user_id
                }).find();
                
                const orderService = think.service('order', 'admin');
                const smsError = await orderService.sendSMS(
                    "2473730",
                    [orderInfo.order_sn, response.data.data.kuaidinum],
                    [`+86${userInfo.mobile || orderInfo.mobile}`],
                );
                if (smsError) {
                    think.logger.error("订单发货成功发送短信通知失败：", smsError);
                }
                
                return this.success({
                    logistic_code: response.data.data.kuaidinum,
                    noodle_taskId: response.data.data.taskId
                });
            }else{
                // todo: 测试环境
                // 订单发货
                await this.orderSendAction(
                    orderInfo, shipperInfo, "测试运单物流号",
                    "测试运单taskId"
                );

                // 发货成功发送短信通知
                // 查询当前订单的用户
                const userInfo = await this.model('user').where({
                    id: orderInfo.user_id
                }).find();
                
                const orderService = think.service('order', 'admin');
                const smsError = await orderService.sendSMS(
                    "2473730",
                    [orderInfo.order_sn, "测试运单物流号"],
                    [`+86${userInfo.mobile || orderInfo.mobile}`],
                );
                if (smsError) {
                    think.logger.error("订单发货成功发送短信通知失败：", smsError);
                }
                
                return this.success({
                    logistic_code: "测试运单物流号",
                    noodle_taskId: "测试运单taskId"
                });
            }
        } catch (error) {
            return this.fail(JSON.stringify(error));
        }
    }

    /**
     * 补打面单
     * @returns 
     */
    async supplementExpressAction() {
        const orderId = this.post('orderId');

        if (think.isEmpty(orderId)) {
            return this.fail('参数错误');
        }

        let orderInfo = await this.model('order').where({
            id: orderId
        }).find();

        if (think.isEmpty(orderInfo)) {
            return this.fail('订单信息错误');
        }

        if (!(orderInfo.order_status == 301)) {
            return this.fail('订单状态错误');
        }

        let expressInfo = await this.model("order_express").where({
            order_id: orderId,
            is_delete: 0
        }).find();

        if (think.isEmpty(expressInfo)) {
            return this.fail('快递信息错误');
        }
        if (expressInfo.delivery_type == 2) {
            return this.fail('该运单是手动发货，不能补打面单');
        }
        if (think.isEmpty(expressInfo.noodle_taskId)) {
            return this.fail('该运单未打印面单，不能补打面单');
        }

        const printOrderUrl = think.config('kuaidi100.printOrderUrl');
        const printKey = think.config('kuaidi100.key');
        const printSecret = think.config('kuaidi100.secret');
        const siid = think.config('kuaidi100.siid');
        let param = {
            siid: siid,
            taskId: expressInfo.noodle_taskId,
        };

        // 当前时间戳
        const t = new  Date().valueOf();
        // 请求签名 32位大写，签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号，
        const sign = md5(JSON.stringify(param) + t + printKey + printSecret).toUpperCase();

        const params = {
            key: printKey,
            sign: sign,
            method: 'printOld', // 注意要加上
            t: t,
            param: JSON.stringify(param) // 这里同样要 stringify
        };

        try {
            think.logger.info("请求复打面单快递100入参：", params);
            const response = await axios.get(printOrderUrl, {
                headers: {
                    'content-type': 'application/x-www-form-urlencoded'
                },  
                params 
            });
            think.logger.info("请求复打面单快递100响应：", response.data);
            if (!(response.data.code == 200 || response.data.code == 30011)) {
                return this.fail(response.data.message);
            }
            
            return this.success();
        } catch (error) {
            think.logger.error("请求复打面单快递100异常：", error);
            return this.fail(JSON.stringify(error));
        }
    }

    async orderSendAction(orderInfo, shipperInfo, logistic_code, noodle_taskId) {
        try {
            await this.transaction('order', async (session) => { 
                // 创建订单快递单成功后，自动发货
                let updateData = {
                    order_status: 301,
                    print_status: 1,
                    shipping_time: parseInt(new Date().getTime() / 1000)
                };
                await this.model('order').db(session).where({
                    id: orderInfo.id
                }).update(updateData);

                let kdData = {
                    order_id: orderInfo.id,
                    shipper_name: shipperInfo.name,
                    shipper_code: shipperInfo.code,
                    logistic_code: logistic_code,
                    noodle_taskId: noodle_taskId,
                    delivery_type: 1,
                    traces: ''
                }
                await this.model('order_express').db(session).add(kdData);

                return true;
            });

            return true;
        } catch (error) {
            think.logger.error("订单发货失败：", error);
            throw this.throwError(-1, "订单发货失败，如已经打印快递面单，请手动发货");
        }
    }

    async getOrderSendExpressListAction() {
        const orderId = this.get('orderId');
        if (think.isEmpty(orderId)) {
            return this.fail('参数错误');
        }
        const expressList = await this.model('order_express').where({
            order_id: orderId,
            is_delete: 0,
            express_type: 1
        }).select();
        return this.success(expressList);
    }

    async getOrderExpressAction() {
        const orderId = this.post('orderId');
        const mobile = this.post('mobile');
        const latestExpressInfo = await this.model('order_express').queryExpress100(orderId, mobile, 1);
        const expressObj = JSON.parse(latestExpressInfo.traces);
        
        return this.success({
          request_time: moment.unix(latestExpressInfo.request_time).format('YYYY-MM-DD HH:mm:ss'),
          is_finish: latestExpressInfo.is_finish,
          traces: expressObj.data || JSON.parse(latestExpressInfo.traces),
          logistic_code: latestExpressInfo.logistic_code,
          shipper_name: latestExpressInfo.shipper_name,
        });
    }

    async queryOrderExpressByIdAction() {
        const expressId = this.post('expressId');
        const expressInfo = await this.model('order_express').where({
            id: expressId
        }).find();
        const latestExpressInfo = await this.model('order_express').queryExpress100(expressInfo.order_id, expressInfo.sender_phone, 1, expressInfo);
        const expressObj = JSON.parse(latestExpressInfo.traces);
        
        return this.success({
          request_time: moment.unix(latestExpressInfo.request_time).format('YYYY-MM-DD HH:mm:ss'),
          is_finish: latestExpressInfo.is_finish,
          traces: expressObj.data || JSON.parse(latestExpressInfo.traces),
          logistic_code: latestExpressInfo.logistic_code,
          shipper_name: latestExpressInfo.shipper_name,
        });
    }

    async getRefundExpressAction() {
        const orderId = this.get('orderId');
        if (think.isEmpty(orderId)) {
            return this.fail('参数错误');
        }
        const expressInfo = await this.model('order_express').where({
            order_id: orderId,
            express_type: 2,
            is_delete: 0
        }).find();
        if (think.isEmpty(expressInfo)) {
            return this.success({
                traces: [],
                logistic_code: "",
            });
        }
        const latestExpressInfo = await this.model('order_express').queryExpress100(orderId, expressInfo.sender_phone, 2, expressInfo);
        const expressObj = JSON.parse(latestExpressInfo.traces);

        return this.success({
            request_time: moment.unix(latestExpressInfo.request_time).format('YYYY-MM-DD HH:mm:ss'),
            is_finish: latestExpressInfo.is_finish,
            traces: expressObj.data || JSON.parse(latestExpressInfo.traces),
            logistic_code: latestExpressInfo.logistic_code,
            shipper_name: latestExpressInfo.shipper_name,
        });
    }

    /**
     * 查询当前订单所有的物流单号
     */
    async getOrderExpressListAction() {
        const orderId = this.get('orderId');
        if (think.isEmpty(orderId)) {
            return this.fail('参数错误');
        }
        const expressList = await this.model('order_express').where({
            order_id: orderId,
            is_delete: 0
        }).select();
        return this.success(expressList);
    }

    async saveOrderExpressAction() {
        const orderId = this.post('orderId');
        const logisticCode = this.post('logisticCode');
        const shipperCode = this.post('shipperCode');
        const expressId = this.post('expressId');

        if (think.isEmpty(orderId) || think.isEmpty(logisticCode) || think.isEmpty(shipperCode)) {
            return this.fail('参数错误');
        }

        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        if (think.isEmpty(orderInfo)) {
            return this.fail('订单信息错误');
        }

        const shipperInfo = await this.model('shipper').where({
            code: shipperCode
        }).find();
        if (think.isEmpty(shipperInfo)) {
            return this.fail('快递公司信息错误');
        }

        
        if (!think.isEmpty(expressId)) {
            await this.model('order_express').where({
                id: expressId,
            }).update({
                logistic_code: logisticCode,
                shipper_name: shipperInfo.name,
                shipper_code: shipperInfo.code,
            });
        } else {
            await this.model('order_express').add({
                logistic_code: logisticCode,
                express_type: 1,
                order_id: orderId,
                shipper_name: shipperInfo.name,
                shipper_code: shipperInfo.code,
                traces: '',
                delivery_type: 2
            });
        }
        return this.success();
    }

    async deleteOrderExpressAction() {
        const id = this.post('id');
        await this.model('order_express').where({
            id: id
        }).update({
            is_delete: 1
        });
        return this.success();
    }
};