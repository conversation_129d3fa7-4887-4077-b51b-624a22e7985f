const Base = require('../../common/controller/base.js');
const moment = require('moment');
const fs = require('fs');
const path = require("path");
module.exports = class extends Base {
    /**
     * index action 商品列表接口
     * @return {Promise} []
     */
    async indexAction() {
        const page = this.get('page') || 1;
        const size = this.get('size');
        const name = this.get('name') || '';
        const category_id = this.get('category_id') || '';
        let where = {
            'g.name': ['like', `%${name}%`],
            'g.is_delete': 0,
        }
        if (category_id) {
            where['g.category_id'] = category_id;
        }
        const model = this.model('goods');
        const data = await model.alias('g')
        .field([
            'g.*', 'gb.brand_name'
        ])
        .join({
            table: 'goods_brand',
            join: 'left',
            as: 'gb',
            on: ['gb.id', 'g.brand_id']
        }).where(where)
        .order(['g.create_time DESC', 'g.sort_order ASC']).page(page, size).countSelect();
        
        for (const item of data.data) {
            // 商品类别
            const info = await this.model('category').where({
                id: item.category_id
            }).find();
            item.category_name = info.name;
            if (item.is_on_sale == 1) {
                item.is_on_sale = true;
            } else {
                item.is_on_sale = false;
            }
            if (item.is_index == 1) {
                item.is_index = true;
            } else {
                item.is_index = false;
            }
            // 商品具体sku 信息
            let product = await this.model('product').where({
                goods_id: item.id,
                is_delete: 0
            }).select();
            for (const ele of product) {
                ele.value = ele.goods_specifition_name;
                ele.is_on_sale = ele.is_on_sale ? "1" : "0";
            }
            item.product = product;
        }
        return this.success(data);
    }
    async getExpressDataAction() {
        let kd = [];
        let cate = [];
        const freightUseType = this.post('freightUseType');
        let where = {
            is_delete: 0,
        };
        if (freightUseType !== undefined) {
            where.freight_use_type = freightUseType;
        }
        const kdData = await this.model('freight_template').where(where).select();
        for (const item of kdData) {
            kd.push({
                value: item.id,
                label: item.name
            })
        }
        const cateData = await this.model('category').where({
            parent_id: 0
        }).select();
        for (const item of cateData) {
            cate.push({
                value: item.id,
                label: item.name
            })
        }
        let infoData = {
            kd: kd,
            cate: cate
        };
        return this.success(infoData);
    }
    async copygoodsAction() {
        try {
            const insertIdRes = await this.transaction('goods', async (session) => {
                const goodsId = this.post('id');
                let data = await this.getModel('goods').where({
                    id: goodsId
                }).find();
                delete data.id;
                data.is_on_sale = 0;
                let insertId = await this.getModel('goods').add(data);
                let goodsGallery = await this.getModel('goods_gallery').where({
                    goods_id: goodsId,
                    is_delete: 0,
                }).select();
                for (const item of goodsGallery) {
                    let gallery = {
                        img_url: item.img_url,
                        sort_order: item.sort_order,
                        goods_id: insertId,
                    }
                    await this.getModel('goods_gallery').add(gallery);
                }

                return insertId;
            });
            return this.success(insertIdRes);
        } catch (error) {
            return this.fail("复制失败");
        }
    }
    async updateStock(goods_sn, goods_number) {
        await this.model('product').where({
            goods_sn: goods_sn
        }).update({
            goods_number: goods_number
        });
    }
    async updateGoodsNumberAction() {
        let all_goods = await this.model('goods').where({
            is_delete: 0,
            is_on_sale: 1
        }).select();
        for (const item of all_goods) {
            let goodsSum = await this.model('product').where({
                goods_id: item.id
            }).sum('goods_number');
            await this.model('goods').where({
                id: item.id
            }).update({
                goods_number: goodsSum
            });
            await think.timeout(2000);
        }
        return this.success();
    }
    async onsaleAction() {
        const page = this.get('page') || 1;
        const size = this.get('size');
        const model = this.model('goods');
        const data = await model.where({
            is_delete: 0,
            is_on_sale: 1
        }).order(['sort_order asc']).page(page, size).countSelect();
        for (const item of data.data) {
            const info = await this.model('category').where({
                id: item.category_id
            }).find();
            item.category_name = info.name;
            // if (info.parent_id != 0) {
            //     const parentInfo = await this.model('category').where({id: info.parent_id}).find();
            //     item.category_p_name = parentInfo.name;
            // }
            if (item.is_on_sale == 1) {
                item.is_on_sale = true;
            } else {
                item.is_on_sale = false;
            }
            if (item.is_index == 1) {
                item.is_index = true;
            } else {
                item.is_index = false;
            }
            let product = await this.model('product').where({
                goods_id: item.id,
                is_delete: 0
            }).select();
            for (const ele of product) {
                ele.value = ele.goods_specifition_name;
                ele.is_on_sale = ele.is_on_sale ? "1" : "0";
            }
            item.product = product;
        }
        return this.success(data);
    }
    async outAction() {
        const page = this.get('page') || 1;
        const size = this.get('size');
        const model = this.model('goods');
        const data = await model.where({
            is_delete: 0,
            goods_number: ['<=', 0]
        }).order(['sort_order asc']).page(page, size).countSelect();
        for (const item of data.data) {
            const info = await this.model('category').where({
                id: item.category_id
            }).find();
            item.category_name = info.name;
            if (item.is_on_sale == 1) {
                item.is_on_sale = true;
            } else {
                item.is_on_sale = false;
            }
            if (item.is_index == 1) {
                item.is_index = true;
            } else {
                item.is_index = false;
            }
            let product = await this.model('product').where({
                goods_id: item.id,
                is_delete: 0
            }).select();
            for (const ele of product) {
                ele.value = ele.goods_specifition_name;
                ele.is_on_sale = ele.is_on_sale ? "1" : "0";
            }
            item.product = product;
        }
        return this.success(data);
    }
    async dropAction() {
        const page = this.get('page') || 1;
        const size = this.get('size');
        const model = this.model('goods');
        const data = await model.where({
            is_delete: 0,
            is_on_sale: 0
        }).order(['id DESC']).page(page, size).countSelect();
        for (const item of data.data) {
            const info = await this.model('category').where({
                id: item.category_id
            }).find();
            item.category_name = info.name;
            if (item.is_on_sale == 1) {
                item.is_on_sale = true;
            } else {
                item.is_on_sale = false;
            }
            if (item.is_index == 1) {
                item.is_index = true;
            } else {
                item.is_index = false;
            }
            let product = await this.model('product').where({
                goods_id: item.id,
                is_delete: 0
            }).select();
            for (const ele of product) {
                ele.value = ele.goods_specifition_name;
                ele.is_on_sale = ele.is_on_sale ? "1" : "0";
            }
            item.product = product;
        }
        return this.success(data);
    }
    async sortAction() {
        const page = this.get('page') || 1;
        const size = this.get('size');
        const model = this.model('goods');
        const index = this.get('index');
        if (index == 1) {
            const data = await model.where({
                is_delete: 0
            }).order(['sell_volume DESC']).page(page, size).countSelect();
            for (const item of data.data) {
                const info = await this.model('category').where({
                    id: item.category_id
                }).find();
                item.category_name = info.name;
                if (item.is_on_sale == 1) {
                    item.is_on_sale = true;
                } else {
                    item.is_on_sale = false;
                }
                if (item.is_index == 1) {
                    item.is_index = true;
                } else {
                    item.is_index = false;
                }
                let product = await this.model('product').where({
                    goods_id: item.id,
                    is_delete: 0
                }).select();
                for (const ele of product) {
                    ele.value = ele.goods_specifition_name;
                    ele.is_on_sale = ele.is_on_sale ? "1" : "0";
                }
                item.product = product;
            }
            return this.success(data);
        } else if (index == 2) {
            const data = await model.where({
                is_delete: 0
            }).order(['retail_price DESC']).page(page, size).countSelect();
            for (const item of data.data) {
                const info = await this.model('category').where({
                    id: item.category_id
                }).find();
                item.category_name = info.name;
                if (item.is_on_sale == 1) {
                    item.is_on_sale = true;
                } else {
                    item.is_on_sale = false;
                }
                if (item.is_index == 1) {
                    item.is_index = true;
                } else {
                    item.is_index = false;
                }
                let product = await this.model('product').where({
                    goods_id: item.id,
                    is_delete: 0
                }).select();
                for (const ele of product) {
                    ele.value = ele.goods_specifition_name;
                    ele.is_on_sale = ele.is_on_sale ? "1" : "0";
                }
                item.product = product;
            }
            return this.success(data);
        } else if (index == 3) {
            const data = await model.where({
                is_delete: 0
            }).order(['goods_number DESC']).page(page, size).countSelect();
            for (const item of data.data) {
                const info = await this.model('category').where({
                    id: item.category_id
                }).find();
                item.category_name = info.name;
                if (item.is_on_sale == 1) {
                    item.is_on_sale = true;
                } else {
                    item.is_on_sale = false;
                }
                if (item.is_index == 1) {
                    item.is_index = true;
                } else {
                    item.is_index = false;
                }
                let product = await this.model('product').where({
                    goods_id: item.id,
                    is_delete: 0
                }).select();
                for (const ele of product) {
                    ele.value = ele.goods_specifition_name;
                    ele.is_on_sale = ele.is_on_sale ? "1" : "0";
                }
                item.product = product;
            }
            return this.success(data);
        }
    }
    async saleStatusAction() {
        const id = this.get('id');
        const status = this.get('status');
        let sale = 0;
        if (status == 'true') {
            sale = 1;
        }
        const model = this.model('goods');
        await model.where({
            id: id
        }).update({
            is_on_sale: sale
        });
        await this.model('cart').where({
            goods_id: id
        }).update({
            is_on_sale: sale,
            checked: sale
        });
    }
    async productStatusAction() {
        const id = this.get('id');
        const status = this.get('status');
        const model = this.model('product');
        await model.where({
            id: id
        }).update({
            is_on_sale: status
        });
		// 4.14更新
		await this.model('cart').where({
			product_id: id,
			is_delete: 0
		}).update({
			is_on_sale: status
		})
    }
    async indexShowStatusAction() {
        const id = this.get('id');
        const status = this.get('status');
        let stat = 0;
        if (status == 'true') {
            stat = 1;
        }
        const model = this.model('goods');
        await model.where({
            id: id
        }).update({
            is_index: stat
        });
    }
    async infoAction() {
        const id = this.get('id');
        const model = this.model('goods');
        const data = await model.where({
            id: id
        }).find();
        let category_id = data.category_id;
        let infoData = {
            info: data,
            category_id: category_id,
        };
        return this.success(infoData);
    }
    // 获取所有商品的 Product 数据
    async getAllProductAction() {
        const goods_id = this.post('goods_id');
        const data = await this.model('product').where({
            goods_id: goods_id,
            is_delete: 0
        }).select();

        return this.success(data);
    }
    async getAllCategory1Action() {
        const model = this.model('category');
        const data = await model.where({
            is_show: 1,
            level: 'L1'
        }).select();
        const c_data = await model.where({
            is_show: 1,
            level: 'L2'
        }).select();
        let newData = [];
        for (const item of data) {
            let children = [];
            for (const citem of c_data) {
                if (citem.parent_id == item.id) {
                    children.push({
                        value: citem.id,
                        label: citem.name
                    })
                }
            }
            newData.push({
                value: item.id,
                label: item.name,
                children: children
            });
        }
        return this.success(newData);
    }
    async getAllCategoryAction() {
        const model = this.model('category');
        const data = await model.where({
            is_show: 1,
            level: 'L1'
        }).field('id,name').select();
        let newData = [];
        for (const item of data) {
            let children = [];
            const c_data = await model.where({
                is_show: 1,
                level: 'L2',
                parent_id: item.id
            }).field('id,name').select();
            for (const c_item of c_data) {
                children.push({
                    value: c_item.id,
                    label: c_item.name
                })
            }
            newData.push({
                value: item.id,
                label: item.name,
                children: children
            });
        }
        return this.success(newData);
    }
    async storeAction() {
        const values = this.post('info');
        const specData = this.post('specData');
        const cateId = this.post('cateId');
        values.category_id = cateId;
        values.is_index = values.is_index ? 1 : 0;
        values.is_new = values.is_new ? 1 : 0;
        
        // 处理运费模式字段
        values.freight_mode = values.freight_mode ? parseInt(values.freight_mode) : 0;
        
        // 如果选择一件包邮，则清空运费模板ID
        if (values.freight_mode === 1) {
            values.freight_template_id = 0;
        }
        
        let id = values.id;
        const insertIdRes = await this.transaction("goods", async (session) => {
            const model = this.model('goods');
            let goods_id = values.id;
            if (id > 0) {
                await model.db(session).where({
                    id: id
                }).update(values);
                await this.model('product').db(session).where({
                    goods_id: id
                }).update({
                    is_delete: 1
                });
                for (const item of specData) {
                    if (item.id > 0) {
                        await this.model('product').db(session).where({
                            id: item.id
                        }).update(item);
                    } else {
                        item.goods_id = id;
                        item.goods_specifition_name = item.goods_specifition_name;
                        await this.model('product').db(session).add(item);
                    }
                }
                for(const [index, item] of values.gallery.entries()){
                    if(item.is_delete == 1 && item.id > 0){
                        await this.model('goods_gallery').db(session).where({
                            id:item.id
                        }).update({
                            is_delete:1
                        })
                    }
                    else if(item.is_delete == 0 && item.id > 0){
                        await this.model('goods_gallery').db(session).where({
                            id:item.id
                        }).update({
                            sort_order:index
                        })
                    }
                    else if(item.is_delete == 0 && item.id == 0){
                        await this.model('goods_gallery').db(session).add({
                            goods_id:id,
                            img_url:item.url,
                            sort_order:index,
                        })
                    }
                }
            } else {
                delete values.id;
    
                goods_id = await model.db(session).add(values);
                for (const item of specData) {
                    item.goods_id = goods_id;
                    item.is_on_sale = 1;
                    item.goods_specifition_name = item.goods_specifition_name;
                    await this.model('product').db(session).add(item);
                }
                for(const [index, item] of values.gallery.entries()){
                    await this.model('goods_gallery').db(session).add({
                        goods_id:goods_id,
                        img_url:item.url,
                        sort_order:index,
                    })
                }
            }
            let pro = await this.model('product').db(session).where({
                goods_id: goods_id,
                is_on_sale: 1,
                is_delete: 0
            }).select();
            if (pro.length > 1) {
                let goodsNum = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).sum('goods_number');
    
                let retail_price = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).getField('retail_price');
                let maxPrice = Math.max(...retail_price);
                let minPrice = Math.min(...retail_price);
    
                let cost = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).getField('cost');
                let maxCost = Math.max(...cost);
                let minCost = Math.min(...cost);
    
                let market = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).getField('market_price');
                let maxMarket = Math.max(...market);
                let minMarket = Math.min(...market);
    
                let agent_price = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).getField('agent_price');
                let maxAgentPrice = Math.max(...agent_price);
                let minAgentPrice = Math.min(...agent_price);
    
                let goodsPrice = '';
                if(minPrice == maxPrice){
                    goodsPrice = minPrice;
                }
                else{
                    goodsPrice = minPrice + '~' + maxPrice;
                }
    
                let costPrice = "";
                if (minCost == maxCost) {
                    costPrice = minCost;
                } else {
                    costPrice = minCost + '~' + maxCost;
                }
    
                let marketPrice = "";
                if (minMarket == maxMarket) {
                    marketPrice = minMarket;
                } else {
                    marketPrice = minMarket + '~' + maxMarket;
                }
    
                let agentPrice = '';
                if (minAgentPrice == maxAgentPrice) {
                    agentPrice = minAgentPrice;
                } else {
                    agentPrice = minAgentPrice + '~' + maxAgentPrice;
                }
    
                await this.model('goods').db(session).where({
                    id: goods_id
                }).update({
                    goods_number: goodsNum,
                    retail_price: goodsPrice,
                    cost_price: costPrice,
                    market_price: marketPrice,
                    min_retail_price: minPrice,
                    min_cost_price: minCost,
                    min_market_price: minMarket,
                    agent_price: agentPrice,
                    min_agent_price: minAgentPrice
                });
            } else {
                if (pro.length > 0) {
                    let info = {
                        goods_number: pro[0].goods_number,
                        retail_price: pro[0].retail_price,
                        cost_price: pro[0].cost,
                        market_price: pro[0].market_price,
                        min_retail_price: pro[0].retail_price,
                        min_cost_price: pro[0].cost,
                        min_market_price: pro[0].market_price,
                        agent_price: pro[0].agent_price,
                        min_agent_price: pro[0].agent_price
                    }
                    await this.model('goods').db(session).where({
                        id: goods_id
                    }).update(info);
                }
            }
    
            return goods_id;
        })
        
        return this.success(insertIdRes);
    }
    async updatePriceAction() {
        let data = this.post('');
		let goods_id = data.goods_id;
        let pro = await this.model('product').where({
		    goods_id: goods_id,
		    is_on_sale: 1,
		    is_delete: 0
		}).select();
		if(pro.length == 0){
			return this.fail(100,'商品的规格数量至少1个')
		}

        await this.transaction('product', async (session) => {
            await this.model('product').db(session).where({
                id: data.id,
            }).update(data);

            await this.model('cart').db(session).where({
                product_id: data.id,
                is_delete: 0,
            }).update({
                retail_price: data.retail_price,
                agent_price: data.agent_price,
                market_price: data.market || 0,
                goods_specifition_name: data.goods_specifition_name,
                goods_sn: data.goods_sn
            });

            if (pro.length > 1) {
                let goodsNum = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).sum('goods_number');

                let retail_price = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).getField('retail_price');
                let maxPrice = Math.max(...retail_price);
                let minPrice = Math.min(...retail_price);

                let cost = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).getField('cost');
                let maxCost = Math.max(...cost);
                let minCost = Math.min(...cost);

                let market = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).getField('market_price');
                let maxMarket = Math.max(...market);
                let minMarket = Math.min(...market);

                let agent_price = await this.model('product').db(session).where({
                    goods_id: goods_id,
                    is_on_sale: 1,
                    is_delete: 0
                }).getField('agent_price');
                let maxAgentPrice = Math.max(...agent_price);
                let minAgentPrice = Math.min(...agent_price);

                let goodsPrice = '';
                if(minPrice == maxPrice){
                    goodsPrice = minPrice;
                }
                else{
                    goodsPrice = minPrice + '~' + maxPrice;
                }

                let costPrice = "";
                if (minCost == maxCost) {
                    costPrice = minCost;
                } else {
                    costPrice = minCost + '~' + maxCost;
                }

                let marketPrice = "";
                if (minMarket == maxMarket) {
                    marketPrice = minMarket;
                } else {
                    marketPrice = minMarket + '~' + maxMarket;
                }

                let agentPrice = '';
                if (minAgentPrice == maxAgentPrice) {
                    agentPrice = minAgentPrice;
                } else {
                    agentPrice = minAgentPrice + '~' + maxAgentPrice;
                }

                await this.model('goods').db(session).where({
                    id: goods_id
                }).update({
                    goods_number: goodsNum,
                    retail_price: goodsPrice,
                    cost_price: costPrice,
                    market_price: marketPrice,
                    min_retail_price: minPrice,
                    min_cost_price: minCost,
                    min_market_price: minMarket,
                    agent_price: agentPrice,
                    min_agent_price: minAgentPrice
                });
            } else if(pro.length == 1){
                let info = {
                    goods_number: pro[0].goods_number,
                    retail_price: pro[0].retail_price,
                    cost_price: pro[0].cost,
                    market_price: pro[0].market_price,
                    min_retail_price: pro[0].retail_price,
                    min_cost_price: pro[0].cost,
                    min_market_price: pro[0].market_price,
                    agent_price: pro[0].agent_price,
                    min_agent_price: pro[0].agent_price
                }
                await this.model('goods').db(session).where({
                    id: goods_id
                }).update(info);
            }
        })

        return this.success();
    }
    async checkSkuAction() {
        const info = this.post('info');
        if (info.id > 0) {
            const model = this.model('product');
            const data = await model.where({
                id: ['<>', info.id],
                goods_sn: info.goods_sn,
                is_delete: 0
            }).find();
            if (!think.isEmpty(data)) {
                return this.fail(100, 'sku重复')
            } else {
                return this.success();
            }
        } else {
            const model = this.model('product');
            const data = await model.where({
                goods_sn: info.goods_sn,
                is_delete: 0
            }).find();
            if (!think.isEmpty(data)) {
                return this.fail(100, 'sku重复')
            } else {
                return this.success();
            }
        }
    }
    async updateSortAction() {
        const id = this.post('id');
        const sort = this.post('sort');
        const model = this.model('goods');
        const data = await model.where({
            id: id
        }).update({
            sort_order: sort
        });
        return this.success(data);
    }
    async updateShortNameAction() {
        const id = this.post('id');
        const short_name = this.post('short_name');
        const model = this.model('goods');
        const data = await model.where({
            id: id
        }).update({
            short_name: short_name
        });
        return this.success(data);
    }
    async galleryListAction() {
        const id = this.get('id');
        const model = this.model('goods_gallery');
        const data = await model.where({
            goods_id: id,
            is_delete:0
        }).select();
        // console.log(data);
        return this.success(data);
    }
    async galleryAction() {
        const url = this.post('url');
        const id = this.post('goods_id');
        let info = {
            goods_id: id,
            img_url: url
        }
        await this.model('goods_gallery').add(info);
        return this.success();
    }
    async getGalleryListAction() {
        const goodsId = this.post('goodsId');
        const data = await this.model('goods_gallery').where({
            goods_id: goodsId,
            is_delete:0
        }).order('sort_order asc').select();
        let galleryData = [];
        for (const item of data) {
            let pdata = {
                id: item.id,
                url: item.img_url,
				is_delete:0,
            }
            galleryData.push(pdata);
        }
        let info = {
            galleryData: galleryData,
        }
        return this.success(info);
    }
    async deleteGalleryFileAction() {
        const url = this.post('url');
        const id = this.post('id');
        await this.model('goods_gallery').where({
            id: id
        }).limit(1).update({
            is_delete: 1
        });
        return this.success('文件删除成功');
    }
    async galleryEditAction() {
        if (!this.isPost) {
            return false;
        }
        const values = this.post();
        let data = values.data;
        // console.log(data);
        const model = this.model('goods_gallery');
        for (const item of data) {
            let id = item.id;
            let sort = parseInt(item.sort_order);
            // console.log(sort);
            await this.model('goods_gallery').where({
                id: id
            }).update({
                sort_order: sort
            });
        }
        return this.success();
    }
    async deleteListPicUrlAction() {
        const id = this.post('id');
        console.log(id);
        await this.model('goods').where({
            id: id
        }).limit(1).update({
            list_pic_url: 0
        });
        return this.success();
    }
    async destoryAction() {
        const id = this.post('id');
        await this.model('goods').where({
            id: id
        }).limit(1).update({
            is_delete: 1
        });
        await this.model('product').where({
            goods_id: id
        }).update({
            is_delete: 1
        });
        await this.model('goods_specification').where({
            goods_id: id
        }).update({
            is_delete: 1
        });
        return this.success();
    }

    // ***************** 商品品牌 ****************//
    async getGoodsBrandAction() {
        // 分页字段
        const page = this.get('page') || 1;
        const size = this.get('size') || 1000000;
        const name = this.get('brand_name') || '';

        const data = await this.model('goods_brand').where({
            brand_name: ['like', `%${name}%`],
            is_delete: 0
        }).page(page, size).countSelect();

        return this.success(data);
    }

    async editGoodsBrandAction() {
        const id = this.post('id');
        const brand_name = this.post('brand_name');
        const info = {
            brand_name: brand_name,
        }
        if (id && id > 0) {
            await this.model('goods_brand').where({
                id: id
            }).update(info);
        } else {
            await this.model('goods_brand').add(info);
        }
        return this.success();
    }

    async deleteGoodsBrandAction() {
        const id = this.post('id');
        await this.model('goods_brand').where({
            id: id
        }).limit(1).update({
            is_delete: 1
        });
        return this.success();
    }

    async getGoodsListByBrandIdAction() {
        const brand_id = this.get('brand_id');
        const page = this.get('page') || 1;
        const size = this.get('size') || 1000000;
        const name = this.get('name') || '';

        const model = this.model('goods');

        const data = await model.alias('g')
        .field([
            'g.*', 'gb.brand_name'
        ])
        .join({
            table: 'goods_brand',
            join: 'left',
            as: 'gb',
            on: ['gb.id', 'g.brand_id']
        }).where({ 'g.name': ['like', `%${name}%`], 'g.brand_id': brand_id, 'g.is_delete': 0 })
        .page(page, size).countSelect();

        return this.success(data);
    }

    async getGoodsListNotInBrandAction() {
        const page = this.get('page') || 1;
        const size = this.get('size') || 1000000;
        const name = this.get('name') || '';

        const model = this.model('goods');

        const data = await model.alias('g')
        .field([
            'g.*', 'gb.brand_name'
        ])
        .join({
            table: 'goods_brand',
            join: 'left',
            as: 'gb',
            on: ['gb.id', 'g.brand_id']
        }).where({ 'g.name': ['like', `%${name}%`], 'g.brand_id': 0, 'g.is_delete': 0 })
        .page(page, size).countSelect();

        return this.success(data);
    }

    async addGoodsToBrandAction() {
        const brand_id = this.post('brand_id');
        const goods_ids = this.post('goods_ids');

        await this.model('goods').where({
            id: ['IN', goods_ids]
        }).update({
            brand_id: brand_id
        });

        return this.success();
    }

    async deleteGoodsFromBrandAction() {
        const goods_id = this.post('goods_id');

        await this.model('goods').where({
            id: goods_id
        }).update({
            brand_id: 0
        });

        return this.success();
    }
};