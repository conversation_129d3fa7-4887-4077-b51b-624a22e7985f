/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-26 18:50:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-26 18:50:00
 * @FilePath: /petshop-server/src/admin/controller/pushTest.js
 * @Description: 推送测试控制器
 */

const Base = require('../../common/controller/base.js');

module.exports = class extends Base {
    /**
     * 测试订单支付推送通知
     */
    async testOrderPaymentPushAction() {
        try {
            // 模拟订单信息
            const mockOrderInfo = {
                id: 999,
                order_sn: 'TEST' + Date.now(),
                actual_price: '99.99',
                user_id: 1
            };

            const payType = this.post('payType') || 'wx'; // wx, ali, balance

            // 获取推送服务
            const pushService = think.service('push', 'common');
            
            // 发送推送通知
            const result = await pushService.sendOrderPaymentNotification(mockOrderInfo, payType);

            if (result) {
                return this.success({
                    message: '推送测试成功',
                    orderInfo: mockOrderInfo,
                    payType: payType
                });
            } else {
                return this.fail('推送测试失败');
            }
        } catch (error) {
            think.logger.error('推送测试异常:', error);
            return this.fail('推送测试异常: ' + error.message);
        }
    }

    /**
     * 测试自定义推送
     */
    async testCustomPushAction() {
        try {
            const title = this.post('title') || '测试推送';
            const content = this.post('content') || '这是一条测试推送消息';
            const mobile = this.post('mobile'); // 指定手机号，不传则推送给所有管理员

            const pushService = think.service('push', 'common');
            
            let aliasList = [];
            if (mobile) {
                aliasList = [mobile];
            } else {
                // 获取所有管理员手机号
                const adminModel = this.model('admin');
                aliasList = await adminModel.getActiveAdminMobiles();
            }

            if (aliasList.length === 0) {
                return this.fail('没有找到有效的推送目标');
            }

            const message = {
                title: title,
                content: content,
                extras: {
                    type: 'test',
                    timestamp: Date.now()
                }
            };

            const result = await pushService.sendPushNotification(aliasList, message);

            if (result) {
                return this.success({
                    message: '自定义推送测试成功',
                    targets: aliasList,
                    pushMessage: message
                });
            } else {
                return this.fail('自定义推送测试失败');
            }
        } catch (error) {
            think.logger.error('自定义推送测试异常:', error);
            return this.fail('自定义推送测试异常: ' + error.message);
        }
    }

    /**
     * 获取管理员列表（用于推送目标查看）
     */
    async getAdminListAction() {
        try {
            const adminModel = this.model('admin');
            const adminList = await adminModel.where({
                is_delete: 0
            }).field('id,username,mobile,status').select();

            const activeMobiles = await adminModel.getActiveAdminMobiles();

            return this.success({
                adminList: adminList,
                activeMobiles: activeMobiles,
                totalCount: adminList.length,
                activeCount: activeMobiles.length
            });
        } catch (error) {
            think.logger.error('获取管理员列表异常:', error);
            return this.fail('获取管理员列表失败: ' + error.message);
        }
    }

    /**
     * 测试订单退款申请推送通知
     */
    async testRefundApplyPushAction() {
        try {
            // 模拟订单信息
            const mockOrderInfo = {
                id: 998,
                order_sn: 'REFUND' + Date.now(),
                actual_price: '199.99',
                user_id: 1,
                pay_type: this.post('payType') || 'wx'
            };

            // 获取推送服务
            const pushService = think.service('push', 'common');
            
            // 发送推送通知
            const result = await pushService.sendOrderRefundApplyNotification(mockOrderInfo);

            if (result) {
                return this.success({
                    message: '退款申请推送测试成功',
                    orderInfo: mockOrderInfo
                });
            } else {
                return this.fail('退款申请推送测试失败');
            }
        } catch (error) {
            think.logger.error('退款申请推送测试异常:', error);
            return this.fail('退款申请推送测试异常: ' + error.message);
        }
    }

    /**
     * 测试退货运单号提交推送通知
     */
    async testRefundExpressPushAction() {
        try {
            // 模拟订单信息
            const mockOrderInfo = {
                id: 997,
                order_sn: 'EXPRESS' + Date.now(),
                actual_price: '299.99',
                user_id: 1
            };

            // 模拟物流信息
            const mockExpressInfo = {
                logistic_code: this.post('logisticCode') || 'SF1234567890',
                shipper_name: this.post('shipperName') || '顺丰速运',
                shipper_code: 'SF',
                sender_name: this.post('senderName') || '张三',
                sender_phone: this.post('senderPhone') || '13800138000'
            };

            // 获取推送服务
            const pushService = think.service('push', 'common');
            
            // 发送推送通知
            const result = await pushService.sendRefundExpressNotification(mockOrderInfo, mockExpressInfo);

            if (result) {
                return this.success({
                    message: '退货运单号推送测试成功',
                    orderInfo: mockOrderInfo,
                    expressInfo: mockExpressInfo
                });
            } else {
                return this.fail('退货运单号推送测试失败');
            }
        } catch (error) {
            think.logger.error('退货运单号推送测试异常:', error);
            return this.fail('退货运单号推送测试异常: ' + error.message);
        }
    }

    /**
     * 测试代理商申请推送通知
     */
    async testAgentApplyPushAction() {
        try {
            // 模拟代理商申请信息
            const mockAgentApplyInfo = {
                id: 996,
                user_id: 1,
                shop_name: this.post('shopName') || '测试宠物店',
                shop_link_name: this.post('linkName') || '张三',
                shop_link_phone: this.post('linkPhone') || '13800138000'
            };

            // 模拟用户信息
            const mockUserInfo = {
                id: 1,
                nickname: this.post('nickname') || '测试用户'
            };

            // 获取推送服务
            const pushService = think.service('push', 'common');
            
            // 发送推送通知
            const result = await pushService.sendAgentApplyNotification(mockAgentApplyInfo, mockUserInfo);

            if (result) {
                return this.success({
                    message: '代理商申请推送测试成功',
                    agentApplyInfo: mockAgentApplyInfo,
                    userInfo: mockUserInfo
                });
            } else {
                return this.fail('代理商申请推送测试失败');
            }
        } catch (error) {
            think.logger.error('代理商申请推送测试异常:', error);
            return this.fail('代理商申请推送测试异常: ' + error.message);
        }
    }

    /**
     * 批量推送测试
     */
    async testBatchPushAction() {
        try {
            const batchSize = parseInt(this.post('batchSize')) || 10;
            const title = this.post('title') || '批量测试推送';
            const content = this.post('content') || '这是一条批量测试推送消息';

            const pushService = think.service('push', 'common');
            const adminModel = this.model('admin');
            
            // 获取所有管理员手机号
            const aliasList = await adminModel.getActiveAdminMobiles();

            if (aliasList.length === 0) {
                return this.fail('没有找到有效的推送目标');
            }

            const message = {
                title: title,
                content: content,
                extras: {
                    type: 'batch_test',
                    timestamp: Date.now(),
                    batchSize: batchSize
                }
            };

            const result = await pushService.sendBatchPushNotification(aliasList, message, batchSize);

            if (result) {
                return this.success({
                    message: '批量推送测试成功',
                    totalTargets: aliasList.length,
                    batchSize: batchSize,
                    batchCount: Math.ceil(aliasList.length / batchSize)
                });
            } else {
                return this.fail('批量推送测试失败');
            }
        } catch (error) {
            think.logger.error('批量推送测试异常:', error);
            return this.fail('批量推送测试异常: ' + error.message);
        }
    }
};