/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-07 17:34:45
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-08 01:42:58
 * @FilePath: /petshop-server/src/api/controller/agentApply.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
const moment = require('moment');
const _ = require('lodash');
const rp = require('request-promise');
const fs = require('fs');
const http = require("http");
module.exports = class extends Base {
    async listAction() {
        const page = this.post('page') || 1;
        const size = this.post('size') || 10;
        const status = this.post('review_status') || '';
        const keyword = this.post('keyword') || '';

        let where = {
            'a.is_delete': 0
        };

        if (!think.isEmpty(keyword)) {
            where._complex = {
                _logic: 'or',
                'u.nickname': ['like', `%${keyword}%`],
                'a.shop_name': ['like', `%${keyword}%`],
                'a.shop_link_name': ['like', `%${keyword}%`],
                'a.shop_link_phone': ['like', `%${keyword}%`]
            };
        }
        if (!think.isEmpty(status)) {
            where['a.review_status'] = status;
        }
        const model = this.model('agent_apply');
        const data = await model.alias('a')
                        .field('a.*,u.nickname,p.id as province,c.id as city,d.id as district,p.name as province_name,c.name as city_name,d.name as district_name')
                        .join({
                            table: 'user',
                            join: 'left',
                            as: 'u',
                            on: ['u.id', 'a.user_id']
                        })
                        .join({
                            table: 'region',
                            join: 'left',
                            as: 'p',
                            on: ['p.id', 'a.province']
                        })
                        .join({
                            table: 'region',
                            join: 'left',
                            as: 'c',
                            on: ['c.id', 'a.city']
                        })
                        .join({
                            table: 'region',
                            join: 'left',
                            as: 'd',
                            on: ['d.id', 'a.district']
                        })
                        .where(where).order(['id DESC'])
                        .page(page, size).countSelect();
        return this.success(data);
    }
    async reviewAction() {
        const id = this.post('id');
        const status = this.post('review_status');
        const refuse_reason = this.post('refuse_reason');
        
        const model = this.model('agent_apply');
        const agentApply = await model.where({
            id: id,
            is_delete: 0
        }).find();
        if (think.isEmpty(agentApply)) {
            return this.fail('申请不存在');
        }
        if (agentApply.review_status == 1) {
            return this.fail('已审核，请勿重复操作');
        }
        let update = {
            review_status: status
        }
        if (status == 2) {
            update.refuse_reason = refuse_reason;
        }
        await model.where({
            id: id
        }).update(update);
        
        if (status == 1) {
            await this.model('user').where({
                id: agentApply.user_id
            }).update({
                is_agent: 1
            });
        }
        return this.success();
    }

    async deleteAction() {
        const id = this.post('id');
        await this.model('agent_apply').where({
            id: id
        }).limit(1).update({
            is_delete: 1
        });
        return this.success();
    }
};