const Base = require('../../common/controller/base.js');
module.exports = class extends Base {
    /**
     * index action
     * @return {Promise} []
     */
    async indexAction() {
        const model = this.model('category');
        const data = await model.where({
            parent_id: 0,
        }).order(['sort_order ASC']).select();
        const topCategory = data.filter((item) => {
            return item.parent_id === 0;
        });
        const categoryList = [];
        topCategory.map((item) => {
            item.level = 1;
            categoryList.push(item);
            data.map((child) => {
                if (child.parent_id === item.id) {
                    child.level = 2;
                    categoryList.push(child);
                }
                if (child.is_show == 1) {
                    child.is_show = true;
                } else {
                    child.is_show = false;
                }
                if (child.is_channel == 1) {
                    child.is_channel = true;
                } else {
                    child.is_channel = false;
                }
                if (child.is_category == 1) {
                    child.is_category = true;
                } else {
                    child.is_category = false;
                }
                if (child.is_top == 1) {
                    child.is_top = true;
                } else {
                    child.is_top = false;
                }
            });
        });
        return this.success(categoryList);
    }
    async updateSortAction() {
        const id = this.post('id');
        const sort = this.post('sort');
        const model = this.model('category');
        const data = await model.where({
            id: id
        }).update({
            sort_order: sort
        });
        return this.success(data);
    }
    async topCategoryAction() {
        const model = this.model('category');
        const data = await model.where({
            parent_id: 0
        }).order(['id ASC']).select();
        return this.success(data);
    }
    async infoAction() {
        const id = this.get('id');
        const model = this.model('category');
        const data = await model.where({
            id: id
        }).find();
        const subCateData = await model.where({
            parent_id: id,
            is_show: 1,
        }).select();
        return this.success({
            data,
            subCateData: subCateData
        });
    }
    async storeAction() {
        if (!this.isPost) {
            return false;
        }
        const values = this.post();
        const id = this.post('id');
        
        values.is_show = values.is_show ? 1 : 0;
        values.is_channel = values.is_channel ? 1 : 0;
        values.is_category = values.is_category ? 1 : 0;

        try {
            await this.transaction('category', async (session) => {
                const model = this.getModel('category');
                if (id > 0) {
                    await model.where({
                        id: id
                    }).update(values);
                } else {
                    delete values.id;
                    await model.add(values);
                }
        
                //! 目前不存在sub_category 所以下面的逻辑不需要 是否存sub_category，如果存在则也更新子类
                // const subCate = values.sub_category;
                // if (subCate) {
                //     if (subCate.length > 0) {
                //         const subCateIds = subCate.map(item => item.id)
                //             .filter(id => id != null && id !== 0 && !isNaN(id));
                //         if (subCateIds.length > 0) {
                //             const condition = {
                //                 parent_id: id
                //             };
                //             condition.id = ['NOTIN', subCateIds];
                // ! 危险操作
                //             await model.where(condition).delete();
                //         }
                //         // 根据入参的数组信息更新子类，不在数组中代表删除
                //         for (let i = 0; i < subCate.length; i++) {
                //             subCate[i].parent_id = id;
                //             if (subCate[i].id > 0) {
                //                 await model.where({
                //                     id: subCate[i].id
                //                 }).update(subCate[i]);
                //             } else {
                //                 delete subCate[i].id;
                //                 await model.add(subCate[i]);
                //             }
                //         }
                //     }else{
                //         // 数组如果为空则删除全部的子类
                // ! 危险操作
                //         await model.where({
                //             parent_id: id
                //         }).delete();
                //     }
                // }
            });

            return this.success();
        } catch (error) {
            return this.fail(-1, error.toString());
        }
    }
    async destoryAction() {
        const id = this.post('id');
        let data = await this.model('category').where({
            parent_id: id
        }).select();
        if (data.length > 0) {
            return this.fail();
        } else {
            await this.model('category').where({
                id: id
            }).update({
                is_delete: 1
            });
            return this.success();
        }
    }
    async showStatusAction() {
        const id = this.get('id');
        const status = this.get('status');
        let ele = 0;
        if (status == 'true') {
            ele = 1;
        }
        const model = this.model('category');
        await model.where({
            id: id
        }).update({
            is_show: ele
        });
    }
    async channelStatusAction() {
        const id = this.get('id');
        const status = this.get('status');
        let stat = 0;
        if (status == 'true') {
            stat = 1;
        }
        const model = this.model('category');
        await model.where({
            id: id
        }).update({
            is_channel: stat
        });
    }
    async categoryStatusAction() {
        const id = this.get('id');
        const status = this.get('status');
        let stat = 0;
        if (status == 'true') {
            stat = 1;
        }
        const model = this.model('category');
        await model.where({
            id: id
        }).update({
            is_category: stat
        });
    }
    async deleteBannerImageAction() {
        let id = this.post('id');
        await this.model('category').where({
            id: id
        }).update({
            img_url: ''
        });
        return this.success();
    }
    async deleteIconImageAction() {
        let id = this.post('id');
        await this.model('category').where({
            id: id
        }).update({
            icon_url: ''
        });
        return this.success();
    }
    async topStatusAction() {
        const id = this.get('id');
        const status = this.get('status');
        let stat = 0;
        if (status == 'true') {
            stat = 1;
        }
        
        const model = this.model('category');
        
        // 如果要设置为置顶状态，先检查是否已经有两个置顶的分类
        if (stat === 1) {
            const currentTopCount = await model.where({
                is_top: 1,
                is_delete: 0
            }).count();
            
            // 如果当前分类已经是置顶状态，不需要检查数量限制
            const currentCategory = await model.where({
                id: id
            }).find();
            
            if (currentCategory.is_top !== 1 && currentTopCount >= 2) {
                return this.fail(-1, '最多只能设置两个置顶分类');
            }
            
            // 当设置为置顶时，自动设置其他字段
            await model.where({
                id: id
            }).update({
                is_top: stat,
                is_channel: 0,  // 设置为0
                is_show: 0,     // 设置为0
                is_category: 1  // 设置为1
            });
        } else {
            // 当取消置顶时，只更新 is_top 字段
            await model.where({
                id: id
            }).update({
                is_top: stat
            });
        }
        
        return this.success();
    }
};