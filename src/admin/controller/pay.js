/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-11 00:56:24
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-15 23:32:05
 * @FilePath: /petshop-server/src/admin/controller/pay.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
const AlipaySdk = require('alipay-sdk');
const fs = require('fs');
const path = require('path');
const Wechatpay = require('wechatpay-node-v3');
const isProd = think.env === 'production';

const alipaySdk = new AlipaySdk({
    appId: '2021005145694479',
    keyType: 'PKCS8',
    privateKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/private-key.pem'), 'ascii'),
    alipayPublicKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/alipay-public-key.pem'), 'ascii'),
});
const wxpay_refund_notify_url = isProd ? 'http://*************:8360/api/pay/wxPayRefundNotify' : 'http://***************:8360/api/pay/wxPayRefundNotify'

const wechatpay = new Wechatpay({
    mchid: '1715440051',
    serial: '7E20A36909FD55C3776D818355C4F1CB07C27EC8',
    privateKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/weixin-private-key.pem'), 'ascii'),
    publicKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/wexin-apiclient_cert.pem'), 'ascii'),
});

module.exports = class extends Base {
    /**
     * 同意退款，等待退货
     */
    async agentRefundAction() {
        const orderSn = this.post('orderSn');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderInfo.id, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能退款');
        }
        // 同意退款，等待退货
        await this.model('order').where({
            order_sn: orderSn,
        }).update({
            order_status: 205,
        })

        // 查询当前订单的用户
        const userInfo = await this.model('user').where({
            id: orderInfo.user_id
        }).find();
        // 发货成功发送短信通知
        const orderService = think.service('order', 'admin');
        const smsError = await orderService.sendSMS(
            "2473731",
            [orderInfo.order_sn],
            [`+86${userInfo.mobile || orderInfo.mobile}`],
        );
        if (smsError) {
            think.logger.error("补充退货物流信息发送短信通知失败：", smsError);
        }

        return this.success();
    }

    /**
     * 部分退款
     */
    async partialRefundAction() {
        const orderSn = this.post('orderSn');
        const refundAmount = this.post('refundAmount');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();
        
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderInfo.id, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能退款');
        }
        // 检查订单金额是否大于部分退款金额
        if (parseFloat(orderInfo.actual_price) < parseFloat(refundAmount)) {
            return this.fail('退款金额必须小于实付金额');
        }
        
        let result = false;
        // 支付宝sdk 发起退款
        if (orderInfo.pay_type === 'ali') {
            result = await alipaySdk.exec('alipay.trade.refund', {
                bizContent: {
                    out_trade_no: orderInfo.order_sn,
                    refund_amount: refundAmount,
                    refund_reason: "订单退款",
                }
            });
            think.logger.info('支付宝申请退款', JSON.stringify(result));
            if (think.isEmpty(result)) {
                return this.fail('退款失败');
            }
        } else if (orderInfo.pay_type === 'wx') {
            result = await wechatpay.refunds({
                out_refund_no: 'wx-refund-' + new Date().getTime() + '-' + orderInfo.order_sn,
                transaction_id: orderInfo.pay_id,
                notify_url: wxpay_refund_notify_url,
                amount: {
                    refund: parseFloat(refundAmount) * 100,
                    total: parseFloat(orderInfo.actual_price) * 100,
                    currency: 'CNY'
                },
                reason: '订单退款'
            });
            think.logger.info('微信申请退款', JSON.stringify(result));
            if (think.isEmpty(result) || result.status != 200) {
                return this.fail('退款失败' || result.error);
            }
        } else if (orderInfo.pay_type === 'balance') {
            // 余额支付的退款处理
            try {
                await this.transaction('order', async (session) => {
                    const userId = orderInfo.user_id;
                    
                    // 1. 查询用户当前余额
                    const user = await this.model('user').db(session).where({
                        id: userId
                    }).find();
                    
                    const currentBalance = parseFloat(user.balance || 0);
                    const refundAmountFloat = parseFloat(refundAmount);
                    const newBalance = currentBalance + refundAmountFloat;
                    
                    // 2. 更新用户余额
                    await this.model('user').db(session).where({
                        id: userId
                    }).update({
                        balance: newBalance
                    });
                    
                    // 3. 添加余额变动记录，包含备注信息
                    const remarkText = `管理员操作：订单部分退款，订单号：${orderInfo.order_sn}，退款金额：${refundAmountFloat}元`;
                    
                    await this.model('balance_log').db(session).add({
                        user_id: userId,
                        amount: refundAmountFloat,
                        balance: newBalance,
                        log_type: 'RF', // RF: 退款
                        order_sn: orderInfo.order_sn,
                        create_time: think.datetime(new Date()),
                        remark: remarkText
                    });
                    
                    // 4. 更新订单状态
                    let updateInfo = {
                        order_status: parseFloat(orderInfo.actual_price) > refundAmountFloat ? 206 : 203, // 部分退款或全额退款
                        refund_time: parseInt(new Date().getTime() / 1000)
                    };
                    
                    await this.model('order').db(session).where({
                        order_sn: orderInfo.order_sn
                    }).update(updateInfo);
                    
                    think.logger.info(`用户 ${userId} 订单 ${orderInfo.order_sn} 余额退款成功，金额: ${refundAmountFloat}，当前余额: ${newBalance}`);
                });
                result = true;
            } catch (error) {
                think.logger.error('处理余额退款时出错:', error);
                return this.fail('退款失败: ' + error.message);
            }
        }

        return this.success();
    }

    /**
     * 订单退款
     * @return {Promise} []
     */
    async cancelRefundAction() {
        const orderSn = this.post('orderSn');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderInfo.id, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能退款');
        }

        return this.refunndFunc(orderInfo);
    }
    /**
     * 强制退款
     */
    async forceRefundAction() {
        const orderSn = this.post('orderSn');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();
        // 检测是否能够取消
        if (think.isEmpty(orderInfo)) {
            return this.fail('订单不存在');
        }
        
        if (!(orderInfo.order_status == 201 || orderInfo.order_status == 202 || orderInfo.order_status == 204 || 
                  orderInfo.order_status == 205 || 
                  orderInfo.order_status == 300 || orderInfo.order_status == 301 || orderInfo.order_status == 302 || 
                  orderInfo.order_status == 303 || orderInfo.order_status == 401)) {
            return this.fail('订单不能退款');
        }

        return this.refunndFunc(orderInfo);
    }

    /**
     * 订单拒绝退款
     */
    async rejectRefundAction() {
        const orderSn = this.post('orderSn');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderInfo.id, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能退款');
        }
        // 拒绝退款
        await this.model('order').where({
            order_sn: orderSn,
        }).update({
            order_status: 204,
        })

        // 查询当前订单的用户
        const userInfo = await this.model('user').where({
            id: orderInfo.user_id
        }).find();
        // 发货成功发送短信通知
        const orderService = think.service('order', 'admin');
        const smsError = await orderService.sendSMS(
            "2474915",
            [orderInfo.order_sn],
            [`+86${userInfo.mobile || orderInfo.mobile}`],
        );
        if (smsError) {
            think.logger.error("拒绝退款发送短信通知失败：", smsError);
        }

        return this.success();
    }

    /**
     * 订单退款
     */
    async refunndFunc(orderInfo) {
        let result = false;
        // 支付宝sdk 发起退款
        if (orderInfo.pay_type === 'ali') {
            result = await alipaySdk.exec('alipay.trade.refund', {
                bizContent: {
                    out_trade_no: orderInfo.order_sn,
                    refund_amount: orderInfo.actual_price,
                    refund_reason: "订单退款",
                }
            });
            think.logger.info('支付宝申请退款', result);
            if (think.isEmpty(result)) {
                return this.fail('支付宝退款失败');
            }else if (result.code !== '10000') {
                return this.fail("支付宝退款失败：" + result.subMsg);
            }
        } else if (orderInfo.pay_type === 'wx') {
            result = await wechatpay.refunds({
                out_refund_no: 'wx-refund-' + new Date().getTime() + '-' + orderInfo.order_sn,
                transaction_id: orderInfo.pay_id,
                notify_url: wxpay_refund_notify_url,
                amount: {
                    refund: parseFloat(orderInfo.actual_price) * 100,
                    total: parseFloat(orderInfo.actual_price) * 100,
                    currency: 'CNY'
                },
                reason: '订单退款'
            });
            think.logger.info('微信申请退款', JSON.stringify(result));
            if (think.isEmpty(result)) {
                return this.fail('微信退款失败');
            }else if (result.status != 200) {
                return this.fail('微信退款失败：' + result.error);
            }
        } else if (orderInfo.pay_type === 'balance') {
            // 余额支付的退款处理
            try {
                await this.transaction('order', async (session) => {
                    const userId = orderInfo.user_id;
                    
                    // 1. 查询用户当前余额
                    const user = await this.model('user').db(session).where({
                        id: userId
                    }).find();
                    
                    const currentBalance = parseFloat(user.balance || 0);
                    const refundAmount = parseFloat(orderInfo.actual_price);
                    const newBalance = currentBalance + refundAmount;
                    
                    // 2. 更新用户余额
                    await this.model('user').db(session).where({
                        id: userId
                    }).update({
                        balance: newBalance
                    });
                    
                    // 3. 添加余额变动记录，包含备注信息
                    const remarkText = `管理员操作：订单全额退款，订单号：${orderInfo.order_sn}，退款金额：${refundAmount}元`;
                    
                    await this.model('balance_log').db(session).add({
                        user_id: userId,
                        amount: refundAmount,
                        balance: newBalance,
                        log_type: 'RF', // RF: 退款
                        order_sn: orderInfo.order_sn,
                        create_time: think.datetime(new Date()),
                        remark: remarkText
                    });
                    
                    // 4. 更新订单状态
                    await this.model('order').db(session).where({
                        order_sn: orderInfo.order_sn
                    }).update({
                        order_status: 203, // 已退款
                        refund_time: parseInt(new Date().getTime() / 1000)
                    });
                    
                    think.logger.info(`用户 ${userId} 订单 ${orderInfo.order_sn} 余额退款成功，金额: ${refundAmount}，当前余额: ${newBalance}`);
                });
                result = true;
            } catch (error) {
                think.logger.error('处理余额退款时出错:', error);
                return this.fail('余额退款失败: ' + error.message);
            }
        }
        
        return this.success();
    }
};
