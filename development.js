/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-21 15:43:48
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 17:03:19
 * @FilePath: /petshop-server/development.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Application = require('thinkjs');
const babel = require('think-babel');
const watcher = require('think-watcher');
const notifier = require('node-notifier');

const instance = new Application({
  ROOT_PATH: __dirname,
  watcher: watcher,
  transpiler: [babel, {
    presets: ['think-node']
  }],
  notifier: notifier.notify.bind(notifier),
  env: 'development'
});

instance.run();
